//! Format conversion utilities

use crate::TuningData;
use anyhow::Result;
use std::path::Path;

/// Format converter
pub struct FormatConverter;

impl FormatConverter {
    /// Convert between formats
    pub fn convert<P: AsRef<Path>>(input_path: P, output_path: P) -> Result<()> {
        // Load from input format
        let data = TuningData::load_from_file(&input_path)?;
        
        // Save to output format
        data.save_to_file(&output_path)?;
        
        Ok(())
    }

    /// Batch convert multiple files
    pub fn batch_convert<P: AsRef<Path>>(
        input_paths: &[P],
        output_dir: P,
        target_format: &str,
    ) -> Result<Vec<String>> {
        let mut converted_files = Vec::new();
        
        for input_path in input_paths {
            let input_path = input_path.as_ref();
            let file_stem = input_path.file_stem()
                .and_then(|s| s.to_str())
                .unwrap_or("converted");
            
            let output_filename = format!("{}.{}", file_stem, target_format);
            let output_path = output_dir.as_ref().join(output_filename);
            
            Self::convert(input_path, &output_path)?;
            converted_files.push(output_path.to_string_lossy().to_string());
        }
        
        Ok(converted_files)
    }
}
