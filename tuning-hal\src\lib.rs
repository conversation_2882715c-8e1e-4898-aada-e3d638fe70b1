//! # Tuning Hardware Abstraction Layer (HAL)
//! 
//! Provides a unified interface for communicating with various tuning hardware
//! including J2534 devices, CAN interfaces, and serial connections.

pub mod device;
pub mod protocol;
pub mod j2534;
#[cfg(target_os = "linux")]
pub mod can;
pub mod serial;
pub mod mock;

use anyhow::Result;
use async_trait::async_trait;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::time::Duration;

/// Main HAL interface for device communication
#[async_trait]
pub trait DeviceInterface: Send + Sync {
    /// Connect to the device
    async fn connect(&mut self) -> Result<()>;
    
    /// Disconnect from the device
    async fn disconnect(&mut self) -> Result<()>;
    
    /// Check if device is connected
    fn is_connected(&self) -> bool;
    
    /// Send raw data to device
    async fn send_raw(&mut self, data: &[u8]) -> Result<()>;
    
    /// Receive raw data from device
    async fn receive_raw(&mut self, timeout: Duration) -> Result<Vec<u8>>;
    
    /// Send a protocol message
    async fn send_message(&mut self, message: &ProtocolMessage) -> Result<()>;
    
    /// Receive a protocol message
    async fn receive_message(&mut self, timeout: Duration) -> Result<ProtocolMessage>;
    
    /// Get device information
    fn get_device_info(&self) -> &DeviceInfo;
    
    /// Get device capabilities
    fn get_capabilities(&self) -> &DeviceCapabilities;
}

/// Device manager for handling multiple devices
pub struct DeviceManager {
    devices: HashMap<String, Box<dyn DeviceInterface>>,
    active_device: Option<String>,
}

impl std::fmt::Debug for DeviceManager {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        f.debug_struct("DeviceManager")
            .field("device_count", &self.devices.len())
            .field("active_device", &self.active_device)
            .finish()
    }
}

impl DeviceManager {
    pub fn new() -> Self {
        Self {
            devices: HashMap::new(),
            active_device: None,
        }
    }

    /// Add a device to the manager
    pub fn add_device(&mut self, name: String, device: Box<dyn DeviceInterface>) {
        self.devices.insert(name, device);
    }

    /// Set the active device
    pub fn set_active_device(&mut self, name: &str) -> Result<()> {
        if self.devices.contains_key(name) {
            self.active_device = Some(name.to_string());
            Ok(())
        } else {
            Err(anyhow::anyhow!("Device not found: {}", name))
        }
    }

    /// Get the active device
    pub fn get_active_device(&mut self) -> Result<&mut Box<dyn DeviceInterface>> {
        if let Some(name) = &self.active_device {
            self.devices.get_mut(name)
                .ok_or_else(|| anyhow::anyhow!("Active device not found"))
        } else {
            Err(anyhow::anyhow!("No active device set"))
        }
    }

    /// Connect to the active device
    pub async fn connect(&mut self) -> Result<()> {
        let device = self.get_active_device()?;
        device.connect().await
    }

    /// Disconnect from the active device
    pub async fn disconnect(&mut self) -> Result<()> {
        let device = self.get_active_device()?;
        device.disconnect().await
    }

    /// Send message using active device
    pub async fn send_message(&mut self, message: &ProtocolMessage) -> Result<()> {
        let device = self.get_active_device()?;
        device.send_message(message).await
    }

    /// Receive message using active device
    pub async fn receive_message(&mut self, timeout: Duration) -> Result<ProtocolMessage> {
        let device = self.get_active_device()?;
        device.receive_message(timeout).await
    }

    /// List available devices
    pub fn list_devices(&self) -> Vec<&String> {
        self.devices.keys().collect()
    }

    /// Get device info for a specific device
    pub fn get_device_info(&self, name: &str) -> Option<&DeviceInfo> {
        self.devices.get(name).map(|d| d.get_device_info())
    }
}

/// Device information structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeviceInfo {
    pub name: String,
    pub device_type: DeviceType,
    pub manufacturer: String,
    pub model: String,
    pub serial_number: String,
    pub firmware_version: String,
    pub hardware_version: String,
}

/// Device types supported by the HAL
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum DeviceType {
    J2534,
    CAN,
    Serial,
    TCP,
    Mock,
}

/// Device capabilities
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeviceCapabilities {
    pub supported_protocols: Vec<ProtocolType>,
    pub max_baud_rate: u32,
    pub supports_can_fd: bool,
    pub supports_j1850: bool,
    pub supports_iso9141: bool,
    pub supports_kwp2000: bool,
    pub supports_iso14230: bool,
    pub buffer_size: usize,
}

/// Protocol message structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProtocolMessage {
    pub protocol: ProtocolType,
    pub data: Vec<u8>,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub direction: MessageDirection,
    pub metadata: HashMap<String, String>,
}

impl ProtocolMessage {
    pub fn new(protocol: ProtocolType, data: Vec<u8>, direction: MessageDirection) -> Self {
        Self {
            protocol,
            data,
            timestamp: chrono::Utc::now(),
            direction,
            metadata: HashMap::new(),
        }
    }

    /// Create a CAN message
    pub fn can_message(can_id: u32, data: Vec<u8>, direction: MessageDirection) -> Self {
        let mut message = Self::new(ProtocolType::CAN, data, direction);
        message.metadata.insert("can_id".to_string(), can_id.to_string());
        message
    }

    /// Create a J2534 message
    pub fn j2534_message(data: Vec<u8>, direction: MessageDirection) -> Self {
        Self::new(ProtocolType::J2534, data, direction)
    }

    /// Get CAN ID if this is a CAN message
    pub fn get_can_id(&self) -> Option<u32> {
        self.metadata.get("can_id")?.parse().ok()
    }
}

/// Supported protocol types
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ProtocolType {
    CAN,
    J1850VPW,
    J1850PWM,
    ISO9141,
    ISO14230,
    J2534,
    Serial,
}

/// Message direction
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum MessageDirection {
    Transmit,
    Receive,
}

/// Device configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeviceConfig {
    pub device_type: DeviceType,
    pub connection_string: String,
    pub baud_rate: Option<u32>,
    pub timeout_ms: u32,
    pub protocol_config: HashMap<String, String>,
}

impl DeviceConfig {
    /// Create a serial device configuration
    pub fn serial(port: &str, baud_rate: u32) -> Self {
        Self {
            device_type: DeviceType::Serial,
            connection_string: port.to_string(),
            baud_rate: Some(baud_rate),
            timeout_ms: 1000,
            protocol_config: HashMap::new(),
        }
    }

    /// Create a CAN device configuration
    pub fn can(interface: &str) -> Self {
        Self {
            device_type: DeviceType::CAN,
            connection_string: interface.to_string(),
            baud_rate: Some(500000), // Default CAN baud rate
            timeout_ms: 1000,
            protocol_config: HashMap::new(),
        }
    }

    /// Create a J2534 device configuration
    pub fn j2534(library_path: &str) -> Self {
        Self {
            device_type: DeviceType::J2534,
            connection_string: library_path.to_string(),
            baud_rate: None,
            timeout_ms: 5000,
            protocol_config: HashMap::new(),
        }
    }

    /// Create a mock device configuration for testing
    pub fn mock(name: &str) -> Self {
        Self {
            device_type: DeviceType::Mock,
            connection_string: name.to_string(),
            baud_rate: None,
            timeout_ms: 100,
            protocol_config: HashMap::new(),
        }
    }
}

/// Factory function to create devices based on configuration
pub fn create_device(config: &DeviceConfig) -> Result<Box<dyn DeviceInterface>> {
    match config.device_type {
        DeviceType::Serial => {
            #[cfg(feature = "serial")]
            {
                Ok(Box::new(serial::SerialDevice::new(config)?))
            }
            #[cfg(not(feature = "serial"))]
            {
                Err(anyhow::anyhow!("Serial support not compiled in"))
            }
        }
        DeviceType::CAN => {
            #[cfg(all(feature = "can", target_os = "linux"))]
            {
                Ok(Box::new(can::CanDevice::new(config)?))
            }
            #[cfg(not(all(feature = "can", target_os = "linux")))]
            {
                Err(anyhow::anyhow!("CAN support only available on Linux"))
            }
        }
        DeviceType::J2534 => {
            #[cfg(feature = "j2534")]
            {
                Ok(Box::new(j2534::J2534Device::new(config)?))
            }
            #[cfg(not(feature = "j2534"))]
            {
                Err(anyhow::anyhow!("J2534 support not compiled in"))
            }
        }
        DeviceType::Mock => {
            Ok(Box::new(mock::MockDevice::new(config)?))
        }
        _ => Err(anyhow::anyhow!("Unsupported device type: {:?}", config.device_type)),
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_device_manager_creation() {
        let manager = DeviceManager::new();
        assert!(manager.devices.is_empty());
        assert!(manager.active_device.is_none());
    }

    #[test]
    fn test_protocol_message_creation() {
        let data = vec![0x01, 0x02, 0x03];
        let message = ProtocolMessage::new(ProtocolType::CAN, data.clone(), MessageDirection::Transmit);
        
        assert_eq!(message.protocol, ProtocolType::CAN);
        assert_eq!(message.data, data);
        assert_eq!(message.direction, MessageDirection::Transmit);
    }

    #[test]
    fn test_can_message_creation() {
        let can_id = 0x123;
        let data = vec![0x01, 0x02, 0x03];
        let message = ProtocolMessage::can_message(can_id, data.clone(), MessageDirection::Transmit);
        
        assert_eq!(message.get_can_id(), Some(can_id));
        assert_eq!(message.data, data);
    }

    #[test]
    fn test_device_config_creation() {
        let serial_config = DeviceConfig::serial("/dev/ttyUSB0", 115200);
        assert_eq!(serial_config.device_type, DeviceType::Serial);
        assert_eq!(serial_config.baud_rate, Some(115200));

        let can_config = DeviceConfig::can("can0");
        assert_eq!(can_config.device_type, DeviceType::CAN);
        
        let mock_config = DeviceConfig::mock("test_device");
        assert_eq!(mock_config.device_type, DeviceType::Mock);
    }
}
