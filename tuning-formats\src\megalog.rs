//! MegaLog file parser and viewer
//! 
//! Supports parsing MegaSquirt log files (.msl, .csv) and provides
//! data analysis and visualization capabilities.

use anyhow::{Result, anyhow};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::Path;
use chrono::{DateTime, Utc, NaiveDateTime};

/// MegaLog data structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MegaLog {
    pub metadata: LogMetadata,
    pub channels: Vec<LogChannel>,
    pub data: Vec<LogRecord>,
    pub statistics: LogStatistics,
}

/// Log file metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogMetadata {
    pub filename: String,
    pub format_version: String,
    pub ecu_signature: String,
    pub start_time: DateTime<Utc>,
    pub end_time: DateTime<Utc>,
    pub duration_seconds: f64,
    pub sample_rate: f64,
    pub record_count: usize,
    pub channel_count: usize,
}

/// Log channel definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogChannel {
    pub index: usize,
    pub name: String,
    pub units: String,
    pub data_type: LogDataType,
    pub scale: f64,
    pub offset: f64,
    pub precision: u8,
    pub min_value: Option<f64>,
    pub max_value: Option<f64>,
}

/// Log data record
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogRecord {
    pub timestamp: f64,
    pub values: Vec<f64>,
}

/// Log statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogStatistics {
    pub channel_stats: HashMap<String, ChannelStatistics>,
    pub total_records: usize,
    pub missing_records: usize,
    pub error_records: usize,
}

/// Channel statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChannelStatistics {
    pub min: f64,
    pub max: f64,
    pub average: f64,
    pub std_deviation: f64,
    pub sample_count: usize,
}

/// Log data types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum LogDataType {
    Integer,
    Float,
    Boolean,
    String,
}

/// MegaLog parser
pub struct MegaLogParser;

impl MegaLogParser {
    /// Parse a MegaLog file
    pub fn parse<P: AsRef<Path>>(path: P) -> Result<MegaLog> {
        let path = path.as_ref();
        let extension = path.extension()
            .and_then(|ext| ext.to_str())
            .unwrap_or("");

        match extension.to_lowercase().as_str() {
            "msl" => Self::parse_msl_file(path),
            "csv" => Self::parse_csv_file(path),
            _ => Err(anyhow!("Unsupported log file format: {}", extension)),
        }
    }

    /// Parse MSL (MegaSquirt Log) binary format
    fn parse_msl_file<P: AsRef<Path>>(path: P) -> Result<MegaLog> {
        let content = std::fs::read(path.as_ref())?;
        
        // MSL files have a specific binary format
        // This is a simplified implementation
        let mut log = MegaLog {
            metadata: LogMetadata {
                filename: path.as_ref().file_name()
                    .unwrap_or_default()
                    .to_string_lossy()
                    .to_string(),
                format_version: "MSL 1.0".to_string(),
                ecu_signature: "Unknown".to_string(),
                start_time: Utc::now(),
                end_time: Utc::now(),
                duration_seconds: 0.0,
                sample_rate: 10.0,
                record_count: 0,
                channel_count: 0,
            },
            channels: Vec::new(),
            data: Vec::new(),
            statistics: LogStatistics {
                channel_stats: HashMap::new(),
                total_records: 0,
                missing_records: 0,
                error_records: 0,
            },
        };

        // Parse MSL header (simplified)
        if content.len() < 64 {
            return Err(anyhow!("Invalid MSL file: too short"));
        }

        // Add common MegaSquirt channels
        log.channels = vec![
            LogChannel {
                index: 0,
                name: "Time".to_string(),
                units: "s".to_string(),
                data_type: LogDataType::Float,
                scale: 1.0,
                offset: 0.0,
                precision: 3,
                min_value: None,
                max_value: None,
            },
            LogChannel {
                index: 1,
                name: "RPM".to_string(),
                units: "RPM".to_string(),
                data_type: LogDataType::Integer,
                scale: 1.0,
                offset: 0.0,
                precision: 0,
                min_value: Some(0.0),
                max_value: Some(8000.0),
            },
            LogChannel {
                index: 2,
                name: "MAP".to_string(),
                units: "kPa".to_string(),
                data_type: LogDataType::Float,
                scale: 1.0,
                offset: 0.0,
                precision: 1,
                min_value: Some(0.0),
                max_value: Some(300.0),
            },
            LogChannel {
                index: 3,
                name: "TPS".to_string(),
                units: "%".to_string(),
                data_type: LogDataType::Float,
                scale: 1.0,
                offset: 0.0,
                precision: 1,
                min_value: Some(0.0),
                max_value: Some(100.0),
            },
            LogChannel {
                index: 4,
                name: "AFR".to_string(),
                units: "AFR".to_string(),
                data_type: LogDataType::Float,
                scale: 1.0,
                offset: 0.0,
                precision: 2,
                min_value: Some(10.0),
                max_value: Some(20.0),
            },
        ];

        log.metadata.channel_count = log.channels.len();

        // Generate sample data for demonstration
        Self::generate_sample_data(&mut log);

        Ok(log)
    }

    /// Parse CSV log file
    fn parse_csv_file<P: AsRef<Path>>(path: P) -> Result<MegaLog> {
        let content = std::fs::read_to_string(path.as_ref())?;
        let mut reader = csv::Reader::from_reader(content.as_bytes());
        
        let mut log = MegaLog {
            metadata: LogMetadata {
                filename: path.as_ref().file_name()
                    .unwrap_or_default()
                    .to_string_lossy()
                    .to_string(),
                format_version: "CSV 1.0".to_string(),
                ecu_signature: "Unknown".to_string(),
                start_time: Utc::now(),
                end_time: Utc::now(),
                duration_seconds: 0.0,
                sample_rate: 10.0,
                record_count: 0,
                channel_count: 0,
            },
            channels: Vec::new(),
            data: Vec::new(),
            statistics: LogStatistics {
                channel_stats: HashMap::new(),
                total_records: 0,
                missing_records: 0,
                error_records: 0,
            },
        };

        // Parse headers
        let headers = reader.headers()?.clone();
        for (index, header) in headers.iter().enumerate() {
            log.channels.push(LogChannel {
                index,
                name: header.to_string(),
                units: Self::guess_units(header),
                data_type: LogDataType::Float,
                scale: 1.0,
                offset: 0.0,
                precision: 2,
                min_value: None,
                max_value: None,
            });
        }

        log.metadata.channel_count = log.channels.len();

        // Parse data records
        for (record_index, result) in reader.records().enumerate() {
            let record = result?;
            let mut values = Vec::new();
            
            for field in record.iter() {
                let value = field.parse::<f64>().unwrap_or(0.0);
                values.push(value);
            }

            log.data.push(LogRecord {
                timestamp: record_index as f64 * 0.1, // Assume 10Hz sampling
                values,
            });
        }

        log.metadata.record_count = log.data.len();
        
        if !log.data.is_empty() {
            log.metadata.duration_seconds = log.data.last().unwrap().timestamp;
        }

        // Calculate statistics
        log.statistics = Self::calculate_statistics(&log);

        Ok(log)
    }

    /// Generate sample data for demonstration
    fn generate_sample_data(log: &mut MegaLog) {
        use std::f64::consts::PI;
        
        let duration = 60.0; // 60 seconds
        let sample_rate = 10.0; // 10 Hz
        let samples = (duration * sample_rate) as usize;

        for i in 0..samples {
            let time = i as f64 / sample_rate;
            let rpm = 2000.0 + 1000.0 * (time * 0.1).sin();
            let map = 100.0 + 50.0 * (time * 0.2).sin();
            let tps = 20.0 + 30.0 * (time * 0.15).sin().abs();
            let afr = 14.7 + 1.0 * (time * 0.3).sin();

            log.data.push(LogRecord {
                timestamp: time,
                values: vec![time, rpm, map, tps, afr],
            });
        }

        log.metadata.record_count = log.data.len();
        log.metadata.duration_seconds = duration;
        log.metadata.start_time = Utc::now() - chrono::Duration::seconds(duration as i64);
        log.metadata.end_time = Utc::now();

        // Calculate statistics
        log.statistics = Self::calculate_statistics(log);
    }

    /// Guess units based on channel name
    fn guess_units(name: &str) -> String {
        let name_lower = name.to_lowercase();
        match name_lower.as_str() {
            name if name.contains("rpm") => "RPM".to_string(),
            name if name.contains("map") => "kPa".to_string(),
            name if name.contains("tps") => "%".to_string(),
            name if name.contains("afr") => "AFR".to_string(),
            name if name.contains("lambda") => "λ".to_string(),
            name if name.contains("temp") => "°C".to_string(),
            name if name.contains("volt") => "V".to_string(),
            name if name.contains("time") => "s".to_string(),
            name if name.contains("speed") => "km/h".to_string(),
            name if name.contains("boost") => "psi".to_string(),
            _ => "".to_string(),
        }
    }

    /// Calculate statistics for all channels
    fn calculate_statistics(log: &MegaLog) -> LogStatistics {
        let mut channel_stats = HashMap::new();

        for (channel_index, channel) in log.channels.iter().enumerate() {
            let values: Vec<f64> = log.data.iter()
                .filter_map(|record| record.values.get(channel_index))
                .copied()
                .collect();

            if !values.is_empty() {
                let min = values.iter().fold(f64::INFINITY, |a, &b| a.min(b));
                let max = values.iter().fold(f64::NEG_INFINITY, |a, &b| a.max(b));
                let sum: f64 = values.iter().sum();
                let average = sum / values.len() as f64;
                
                let variance: f64 = values.iter()
                    .map(|&x| (x - average).powi(2))
                    .sum::<f64>() / values.len() as f64;
                let std_deviation = variance.sqrt();

                channel_stats.insert(channel.name.clone(), ChannelStatistics {
                    min,
                    max,
                    average,
                    std_deviation,
                    sample_count: values.len(),
                });
            }
        }

        LogStatistics {
            channel_stats,
            total_records: log.data.len(),
            missing_records: 0,
            error_records: 0,
        }
    }

    /// Export log data to CSV
    pub fn export_csv<P: AsRef<Path>>(log: &MegaLog, path: P) -> Result<()> {
        let mut writer = csv::Writer::from_path(path)?;

        // Write headers
        let headers: Vec<&str> = log.channels.iter().map(|c| c.name.as_str()).collect();
        writer.write_record(&headers)?;

        // Write data
        for record in &log.data {
            let values: Vec<String> = record.values.iter().map(|v| v.to_string()).collect();
            writer.write_record(&values)?;
        }

        writer.flush()?;
        Ok(())
    }

    /// Get data for specific time range
    pub fn get_time_range(log: &MegaLog, start_time: f64, end_time: f64) -> Vec<LogRecord> {
        log.data.iter()
            .filter(|record| record.timestamp >= start_time && record.timestamp <= end_time)
            .cloned()
            .collect()
    }

    /// Get data for specific channels
    pub fn get_channels(log: &MegaLog, channel_names: &[String]) -> Result<Vec<Vec<f64>>> {
        let mut channel_indices = Vec::new();
        
        for name in channel_names {
            if let Some(channel) = log.channels.iter().find(|c| &c.name == name) {
                channel_indices.push(channel.index);
            } else {
                return Err(anyhow!("Channel not found: {}", name));
            }
        }

        let mut result = vec![Vec::new(); channel_indices.len()];
        
        for record in &log.data {
            for (i, &channel_index) in channel_indices.iter().enumerate() {
                if let Some(&value) = record.values.get(channel_index) {
                    result[i].push(value);
                }
            }
        }

        Ok(result)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_guess_units() {
        assert_eq!(MegaLogParser::guess_units("RPM"), "RPM");
        assert_eq!(MegaLogParser::guess_units("MAP"), "kPa");
        assert_eq!(MegaLogParser::guess_units("TPS"), "%");
        assert_eq!(MegaLogParser::guess_units("AFR"), "AFR");
        assert_eq!(MegaLogParser::guess_units("coolant_temp"), "°C");
    }

    #[test]
    fn test_sample_data_generation() {
        let mut log = MegaLog {
            metadata: LogMetadata {
                filename: "test.msl".to_string(),
                format_version: "MSL 1.0".to_string(),
                ecu_signature: "Test".to_string(),
                start_time: Utc::now(),
                end_time: Utc::now(),
                duration_seconds: 0.0,
                sample_rate: 10.0,
                record_count: 0,
                channel_count: 5,
            },
            channels: vec![
                LogChannel {
                    index: 0,
                    name: "Time".to_string(),
                    units: "s".to_string(),
                    data_type: LogDataType::Float,
                    scale: 1.0,
                    offset: 0.0,
                    precision: 3,
                    min_value: None,
                    max_value: None,
                },
            ],
            data: Vec::new(),
            statistics: LogStatistics {
                channel_stats: HashMap::new(),
                total_records: 0,
                missing_records: 0,
                error_records: 0,
            },
        };

        MegaLogParser::generate_sample_data(&mut log);
        
        assert!(!log.data.is_empty());
        assert!(log.metadata.duration_seconds > 0.0);
        assert!(!log.statistics.channel_stats.is_empty());
    }
}
