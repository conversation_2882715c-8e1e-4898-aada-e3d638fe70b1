//! # Safety Monitoring System
//! 
//! Comprehensive safety monitoring and validation system for tuning operations.

use anyhow::{Result, anyhow};
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use std::collections::HashMap;
use crate::{TuningRequest, torque::SafetyLimits};

/// Safety monitoring system
#[derive(Debug)]
pub struct SafetyMonitor {
    config: SafetyConfig,
    active_alerts: Vec<SafetyAlert>,
    violation_history: Vec<SafetyViolation>,
}

impl SafetyMonitor {
    pub fn new(config: &SafetyConfig) -> Result<Self> {
        Ok(Self {
            config: config.clone(),
            active_alerts: Vec::new(),
            violation_history: Vec::new(),
        })
    }

    /// Validate a tuning request against safety parameters
    pub fn validate_request(&mut self, request: &TuningRequest) -> Result<()> {
        let mut violations = Vec::new();

        // Check torque limits
        if request.torque_demand.driver_demand > self.config.max_torque {
            violations.push(SafetyViolation {
                violation_type: ViolationType::TorqueExceeded,
                value: request.torque_demand.driver_demand,
                limit: self.config.max_torque,
                timestamp: Utc::now(),
                severity: Severity::Critical,
            });
        }

        // Check RPM limits
        if request.torque_demand.engine_speed > self.config.max_rpm {
            violations.push(SafetyViolation {
                violation_type: ViolationType::RpmExceeded,
                value: request.torque_demand.engine_speed,
                limit: self.config.max_rpm,
                timestamp: Utc::now(),
                severity: Severity::Critical,
            });
        }

        // Check throttle position sanity
        if request.torque_demand.throttle_position > 100.0 || request.torque_demand.throttle_position < 0.0 {
            violations.push(SafetyViolation {
                violation_type: ViolationType::InvalidThrottlePosition,
                value: request.torque_demand.throttle_position,
                limit: 100.0,
                timestamp: Utc::now(),
                severity: Severity::Warning,
            });
        }

        // Check for emergency conditions
        if request.torque_demand.safety_limits.emergency_cutoff {
            return Err(anyhow!("Emergency cutoff activated"));
        }

        // Process violations
        for violation in violations {
            self.violation_history.push(violation.clone());
            
            match violation.severity {
                Severity::Critical => {
                    self.active_alerts.push(SafetyAlert {
                        id: uuid::Uuid::new_v4(),
                        alert_type: AlertType::SafetyViolation,
                        message: format!("Critical safety violation: {:?}", violation.violation_type),
                        severity: violation.severity,
                        timestamp: Utc::now(),
                        acknowledged: false,
                    });
                    return Err(anyhow!("Critical safety violation: {:?}", violation.violation_type));
                }
                Severity::Warning => {
                    self.active_alerts.push(SafetyAlert {
                        id: uuid::Uuid::new_v4(),
                        alert_type: AlertType::Warning,
                        message: format!("Safety warning: {:?}", violation.violation_type),
                        severity: violation.severity,
                        timestamp: Utc::now(),
                        acknowledged: false,
                    });
                }
                Severity::Info => {
                    // Log but don't block
                    tracing::info!("Safety info: {:?}", violation);
                }
            }
        }

        Ok(())
    }

    /// Get current safety status
    pub fn get_status(&self) -> SafetyStatus {
        SafetyStatus {
            active_alerts: self.active_alerts.clone(),
            recent_violations: self.violation_history
                .iter()
                .rev()
                .take(10)
                .cloned()
                .collect(),
            system_armed: self.config.system_armed,
            last_check: Utc::now(),
        }
    }

    /// Acknowledge a safety alert
    pub fn acknowledge_alert(&mut self, alert_id: uuid::Uuid) -> Result<()> {
        if let Some(alert) = self.active_alerts.iter_mut().find(|a| a.id == alert_id) {
            alert.acknowledged = true;
            Ok(())
        } else {
            Err(anyhow!("Alert not found: {}", alert_id))
        }
    }

    /// Clear acknowledged alerts
    pub fn clear_acknowledged_alerts(&mut self) {
        self.active_alerts.retain(|alert| !alert.acknowledged);
    }
}

/// Safety configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SafetyConfig {
    pub max_torque: f64,
    pub max_rpm: f64,
    pub max_boost: f64,
    pub max_egt: f64,
    pub system_armed: bool,
    pub auto_cutoff_enabled: bool,
}

impl Default for SafetyConfig {
    fn default() -> Self {
        Self {
            max_torque: 500.0,
            max_rpm: 7000.0,
            max_boost: 2.0,
            max_egt: 950.0,
            system_armed: true,
            auto_cutoff_enabled: true,
        }
    }
}

/// Safety overrides for special conditions
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SafetyOverrides {
    pub override_torque_limit: Option<f64>,
    pub override_rpm_limit: Option<f64>,
    pub disable_auto_cutoff: bool,
    pub reason: String,
    pub authorized_by: String,
}

/// Safety violation record
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SafetyViolation {
    pub violation_type: ViolationType,
    pub value: f64,
    pub limit: f64,
    pub timestamp: DateTime<Utc>,
    pub severity: Severity,
}

/// Types of safety violations
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ViolationType {
    TorqueExceeded,
    RpmExceeded,
    BoostExceeded,
    EgtExceeded,
    InvalidThrottlePosition,
    CommunicationTimeout,
    SensorFailure,
}

/// Safety alert structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SafetyAlert {
    pub id: uuid::Uuid,
    pub alert_type: AlertType,
    pub message: String,
    pub severity: Severity,
    pub timestamp: DateTime<Utc>,
    pub acknowledged: bool,
}

/// Alert types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AlertType {
    SafetyViolation,
    Warning,
    SystemStatus,
    CommunicationError,
}

/// Severity levels
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum Severity {
    Critical,
    Warning,
    Info,
}

/// Safety system status
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SafetyStatus {
    pub active_alerts: Vec<SafetyAlert>,
    pub recent_violations: Vec<SafetyViolation>,
    pub system_armed: bool,
    pub last_check: DateTime<Utc>,
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::torque::TorqueDemand;

    #[test]
    fn test_safety_monitor_creation() {
        let config = SafetyConfig::default();
        let monitor = SafetyMonitor::new(&config);
        assert!(monitor.is_ok());
    }

    #[test]
    fn test_torque_limit_validation() {
        let config = SafetyConfig {
            max_torque: 300.0,
            ..Default::default()
        };
        let mut monitor = SafetyMonitor::new(&config).unwrap();

        let request = TuningRequest {
            id: uuid::Uuid::new_v4(),
            timestamp: Utc::now(),
            torque_demand: TorqueDemand {
                driver_demand: 400.0, // Exceeds limit
                ..Default::default()
            },
            engine_parameters: HashMap::new(),
            safety_overrides: None,
        };

        let result = monitor.validate_request(&request);
        assert!(result.is_err());
    }

    #[test]
    fn test_valid_request() {
        let config = SafetyConfig::default();
        let mut monitor = SafetyMonitor::new(&config).unwrap();

        let request = TuningRequest {
            id: uuid::Uuid::new_v4(),
            timestamp: Utc::now(),
            torque_demand: TorqueDemand {
                driver_demand: 200.0, // Within limits
                engine_speed: 3000.0,
                throttle_position: 50.0,
                ..Default::default()
            },
            engine_parameters: HashMap::new(),
            safety_overrides: None,
        };

        let result = monitor.validate_request(&request);
        assert!(result.is_ok());
    }
}
