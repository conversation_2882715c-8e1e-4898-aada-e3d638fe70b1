# Getting Started with Universal Tuning Software

## 🚀 Quick Start Guide

This guide will help you set up and run the Universal Tuning Software on your system.

## 📋 Prerequisites

### 1. Install Rust

The tuning software is built with Rust. Install it from [rustup.rs](https://rustup.rs/):

**Windows:**
```powershell
# Download and run rustup-init.exe from https://rustup.rs/
# Or use winget:
winget install Rustlang.Rustup
```

**Linux/macOS:**
```bash
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env
```

### 2. Verify Installation

```bash
rustc --version
cargo --version
```

You should see output similar to:
```
rustc 1.75.0 (82e1608df 2023-12-21)
cargo 1.75.0 (1d8b05cdd 2023-11-20)
```

## 🔧 Building the Project

### 1. Clone or Navigate to Project Directory

```bash
cd "c:\Users\<USER>\Documents\augment-projects\tuning software"
```

### 2. Build the Project

```bash
# Check for compilation errors
cargo check

# Build in debug mode
cargo build

# Build optimized release version
cargo build --release
```

### 3. Run Tests

```bash
# Run all tests
cargo test

# Run tests for specific component
cargo test -p tuning-core
cargo test -p tuning-hal
cargo test -p tuning-formats
```

## 🏃‍♂️ Running the Application

### 1. Basic Usage (Mock Device)

```bash
# Run with default settings (mock device)
cargo run

# Or run the release version
cargo run --release
```

The application will start with:
- Mock device for testing
- Web interface on http://localhost:8080
- Default configuration

### 2. With Custom Configuration

```bash
# Use custom configuration file
cargo run -- --config examples/config.yaml

# Specify different host/port
cargo run -- --host 0.0.0.0 --port 3000
```

### 3. With Real Hardware

```bash
# Serial device
cargo run -- --device serial:/dev/ttyUSB0:115200

# CAN interface (Linux)
cargo run -- --device can:can0

# J2534 device (Windows)
cargo run -- --device j2534:"C:\Program Files\MyDevice\j2534.dll"
```

### 4. Complete Example

```bash
cargo run -- \
  --config examples/config.yaml \
  --host 127.0.0.1 \
  --port 8080 \
  --device mock:test_engine
```

## 🌐 Web Interface

Once the application is running, open your browser and navigate to:

```
http://localhost:8080
```

The web interface provides:
- **Real-time Engine Data**: RPM, torque, throttle position, MAP
- **Safety Monitoring**: Active alerts, system status
- **Device Management**: Connection status, device info
- **Performance Metrics**: Operation counts, success rates
- **Interactive Controls**: Connect/disconnect, test requests
- **Live Logging**: System events and diagnostics

### WebSocket API

The application provides a WebSocket endpoint at `/ws` for real-time communication:

```javascript
const ws = new WebSocket('ws://localhost:8080/ws');

// Send a torque request
const request = {
    type: 'TorqueRequest',
    request: {
        id: crypto.randomUUID(),
        timestamp: new Date().toISOString(),
        torque_demand: {
            driver_demand: 200.0,
            system_request: 0.0,
            safety_limits: {
                max_torque: 500.0,
                max_rpm: 7000.0,
                max_boost: 2.0,
                max_egt: 950.0,
                emergency_cutoff: false
            },
            engine_speed: 3000.0,
            throttle_position: 40.0,
            load_factor: 0.5
        },
        engine_parameters: {},
        safety_overrides: null
    }
};

ws.send(JSON.stringify(request));
```

## 📁 File Format Conversion

The software supports conversion between multiple tuning file formats:

```bash
# Using the library in your own code
use tuning_formats::TuningData;

// Load from any supported format
let data = TuningData::load_from_file("tune.msq")?;

// Convert to another format
data.save_to_file("tune.yaml")?;
data.save_to_file("tune.json")?;

// Validate data integrity
let validation_report = data.validate()?;
```

## 🔧 Development

### Project Structure

```
tuning-software/
├── src/main.rs              # Main application entry point
├── tuning-core/             # Core tuning engine
│   ├── src/lib.rs
│   ├── src/torque.rs        # Torque management system
│   ├── src/safety.rs        # Safety monitoring
│   ├── src/diagnostics.rs   # Diagnostics engine
│   ├── src/config.rs        # Configuration management
│   └── src/engine.rs        # Engine management
├── tuning-hal/              # Hardware abstraction layer
│   ├── src/lib.rs
│   ├── src/j2534.rs         # J2534 support
│   ├── src/can.rs           # CAN interface
│   ├── src/serial.rs        # Serial communication
│   └── src/mock.rs          # Mock device for testing
├── tuning-formats/          # File format conversion
│   ├── src/lib.rs
│   ├── src/msq.rs           # MegaSquirt format
│   ├── src/yaml.rs          # YAML format
│   ├── src/json.rs          # JSON format
│   └── src/converter.rs     # Format conversion utilities
├── tuning-web/              # Web interface
│   ├── src/lib.rs
│   ├── src/server.rs        # Web server
│   ├── src/api.rs           # REST API
│   └── src/websocket.rs     # WebSocket handling
├── web/                     # Static web files
│   └── index.html           # Main web interface
└── examples/                # Example configurations
    └── config.yaml          # Sample configuration
```

### Adding New Features

1. **New Device Support**: Add to `tuning-hal/src/`
2. **New File Formats**: Add to `tuning-formats/src/`
3. **New Safety Features**: Extend `tuning-core/src/safety.rs`
4. **Web Interface**: Modify `web/index.html` and `tuning-web/src/`

### Running in Development Mode

```bash
# Enable debug logging
RUST_LOG=debug cargo run

# Run with hot reload (requires cargo-watch)
cargo install cargo-watch
cargo watch -x run
```

## 🐛 Troubleshooting

### Common Issues

1. **Compilation Errors**
   ```bash
   # Update Rust toolchain
   rustup update
   
   # Clean build cache
   cargo clean
   cargo build
   ```

2. **Device Connection Issues**
   - Check device permissions (Linux: add user to dialout group)
   - Verify device path and baud rate
   - Try mock device first: `--device mock:test`

3. **Web Interface Not Loading**
   - Check if port is already in use
   - Try different port: `--port 3000`
   - Check firewall settings

4. **Performance Issues**
   - Use release build: `cargo run --release`
   - Adjust logging level: `RUST_LOG=warn cargo run`

### Getting Help

- **Documentation**: Check the README.md and inline code documentation
- **Issues**: Report bugs and feature requests on GitHub
- **Community**: Join our Discord server for real-time help

## 🎯 Next Steps

1. **Explore the Web Interface**: Connect to http://localhost:8080
2. **Try Different Configurations**: Modify `examples/config.yaml`
3. **Test File Conversion**: Convert between .msq, .yaml, and .json formats
4. **Connect Real Hardware**: Use J2534 or CAN devices
5. **Customize Safety Limits**: Adjust for your specific engine
6. **Integrate with Existing Tools**: Use the API for custom applications

## 📚 Additional Resources

- **Rust Documentation**: https://doc.rust-lang.org/
- **Tokio Async Runtime**: https://tokio.rs/
- **Axum Web Framework**: https://github.com/tokio-rs/axum
- **Serde Serialization**: https://serde.rs/

Happy tuning! 🏎️💨
