//! Main application entry point

use anyhow::Result;
use clap::{Arg, Command};
use std::sync::Arc;
use tracing_subscriber;

use tuning_core::{TuningEngine, config::EngineConfig};
use tuning_hal::{<PERSON><PERSON>Mana<PERSON>, DeviceConfig, create_device};
use tuning_web::{server, AppState, WebConfig, RealTimeDataCollector};

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize logging
    tracing_subscriber::fmt::init();

    // Parse command line arguments
    let matches = Command::new("tuning-software")
        .version("0.1.0")
        .about("Universal Tuning Software with torque-centric architecture")
        .arg(
            Arg::new("config")
                .short('c')
                .long("config")
                .value_name("FILE")
                .help("Configuration file path")
        )
        .arg(
            Arg::new("host")
                .long("host")
                .value_name("HOST")
                .default_value("127.0.0.1")
                .help("Web server host")
        )
        .arg(
            Arg::new("port")
                .short('p')
                .long("port")
                .value_name("PORT")
                .default_value("8080")
                .help("Web server port")
        )
        .arg(
            Arg::new("device")
                .short('d')
                .long("device")
                .value_name("DEVICE")
                .help("Device configuration (e.g., 'mock:test', 'serial:/dev/ttyUSB0:115200', 'can:can0')")
        )
        .get_matches();

    // Load configuration
    let engine_config = if let Some(config_path) = matches.get_one::<String>("config") {
        tracing::info!("Loading configuration from: {}", config_path);
        EngineConfig::load_from_file(config_path)?
    } else {
        tracing::info!("Using default configuration");
        EngineConfig::default()
    };

    // Validate configuration
    engine_config.validate()?;
    tracing::info!("Configuration validated successfully");

    // Initialize tuning engine
    let tuning_engine = TuningEngine::new(engine_config)?;
    tracing::info!("Tuning engine initialized");

    // Initialize device manager
    let mut device_manager = DeviceManager::new();

    // Add device if specified
    if let Some(device_spec) = matches.get_one::<String>("device") {
        let device_config = parse_device_spec(device_spec)?;
        let device = create_device(&device_config)?;
        device_manager.add_device("main".to_string(), device);
        device_manager.set_active_device("main")?;
        tracing::info!("Device configured: {:?}", device_config.device_type);
    } else {
        // Add a mock device by default
        let mock_config = DeviceConfig::mock("default");
        let mock_device = create_device(&mock_config)?;
        device_manager.add_device("mock".to_string(), mock_device);
        device_manager.set_active_device("mock")?;
        tracing::info!("Using mock device for testing");
    }

    // Create web configuration
    let web_config = WebConfig {
        host: matches.get_one::<String>("host").unwrap().clone(),
        port: matches.get_one::<String>("port").unwrap().parse()?,
        ..Default::default()
    };

    // Create application state
    let app_state = Arc::new(AppState::new(tuning_engine, device_manager));

    // Start real-time data collector
    let data_collector = RealTimeDataCollector::new(app_state.clone(), 100); // 100ms interval
    let app_state_clone = app_state.clone();
    tokio::spawn(async move {
        if let Err(e) = data_collector.start().await {
            tracing::error!("Real-time data collector error: {}", e);
        }
    });

    // Connect to device
    {
        let mut device_manager = app_state_clone.device_manager.lock().await;
        if let Err(e) = device_manager.connect().await {
            tracing::warn!("Failed to connect to device: {}", e);
        } else {
            tracing::info!("Connected to device successfully");
        }
    }

    // Start web server
    tracing::info!("Starting tuning software...");
    server::start_server(web_config, app_state).await?;

    Ok(())
}

/// Parse device specification string
fn parse_device_spec(spec: &str) -> Result<DeviceConfig> {
    let parts: Vec<&str> = spec.split(':').collect();
    
    match parts.as_slice() {
        ["mock", name] => Ok(DeviceConfig::mock(name)),
        ["serial", port, baud] => {
            let baud_rate = baud.parse()?;
            Ok(DeviceConfig::serial(port, baud_rate))
        }
        ["serial", port] => Ok(DeviceConfig::serial(port, 115200)),
        ["can", interface] => Ok(DeviceConfig::can(interface)),
        ["j2534", library] => Ok(DeviceConfig::j2534(library)),
        _ => Err(anyhow::anyhow!("Invalid device specification: {}", spec)),
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parse_device_spec() {
        let mock_config = parse_device_spec("mock:test").unwrap();
        assert_eq!(mock_config.device_type, tuning_hal::DeviceType::Mock);

        let serial_config = parse_device_spec("serial:/dev/ttyUSB0:115200").unwrap();
        assert_eq!(serial_config.device_type, tuning_hal::DeviceType::Serial);
        assert_eq!(serial_config.baud_rate, Some(115200));

        let can_config = parse_device_spec("can:can0").unwrap();
        assert_eq!(can_config.device_type, tuning_hal::DeviceType::CAN);
    }
}
