//! TunerStudio Dashboard (.dash) file parser
//! 
//! Supports parsing and rendering TunerStudio dashboard files which define
//! custom gauge layouts and real-time data displays.

use anyhow::{Result, anyhow};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::Path;

/// Dashboard file structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Dashboard {
    pub metadata: DashMetadata,
    pub gauges: Vec<DashGauge>,
    pub indicators: Vec<DashIndicator>,
    pub graphs: Vec<DashGraph>,
    pub layout: DashLayout,
}

/// Dashboard metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DashMetadata {
    pub name: String,
    pub version: String,
    pub author: String,
    pub description: String,
    pub created: String,
    pub modified: String,
}

/// Dashboard gauge definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DashGauge {
    pub id: String,
    pub name: String,
    pub variable: String,
    pub position: Position,
    pub size: Size,
    pub gauge_type: GaugeType,
    pub min_value: f64,
    pub max_value: f64,
    pub units: String,
    pub warning_ranges: Vec<ValueRange>,
    pub danger_ranges: Vec<ValueRange>,
    pub style: GaugeStyle,
}

/// Dashboard indicator (warning lights, etc.)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DashIndicator {
    pub id: String,
    pub name: String,
    pub variable: String,
    pub position: Position,
    pub size: Size,
    pub indicator_type: IndicatorType,
    pub conditions: Vec<IndicatorCondition>,
    pub style: IndicatorStyle,
}

/// Dashboard graph for data plotting
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DashGraph {
    pub id: String,
    pub name: String,
    pub variables: Vec<String>,
    pub position: Position,
    pub size: Size,
    pub graph_type: GraphType,
    pub time_window: f64, // seconds
    pub y_min: Option<f64>,
    pub y_max: Option<f64>,
    pub style: GraphStyle,
}

/// Dashboard layout configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DashLayout {
    pub width: u32,
    pub height: u32,
    pub background_color: String,
    pub background_image: Option<String>,
    pub grid_enabled: bool,
    pub grid_size: u32,
}

/// Position on dashboard
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Position {
    pub x: u32,
    pub y: u32,
}

/// Size of dashboard element
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Size {
    pub width: u32,
    pub height: u32,
}

/// Value range for warnings/dangers
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValueRange {
    pub min: f64,
    pub max: f64,
    pub color: String,
}

/// Gauge types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum GaugeType {
    Analog,      // Traditional round gauge
    Digital,     // Digital number display
    Bar,         // Horizontal/vertical bar
    Led,         // LED strip
    Text,        // Text display
}

/// Indicator types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum IndicatorType {
    Light,       // Warning light
    Icon,        // Custom icon
    Text,        // Text indicator
    Shape,       // Geometric shape
}

/// Graph types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum GraphType {
    Line,        // Line graph
    Bar,         // Bar chart
    Scatter,     // Scatter plot
    Area,        // Area chart
}

/// Indicator condition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IndicatorCondition {
    pub condition_type: ConditionType,
    pub value: f64,
    pub action: IndicatorAction,
}

/// Condition types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ConditionType {
    GreaterThan,
    LessThan,
    Equal,
    NotEqual,
    Between,
    Outside,
}

/// Indicator actions
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum IndicatorAction {
    Show,
    Hide,
    Blink,
    ChangeColor(String),
}

/// Gauge styling
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GaugeStyle {
    pub background_color: String,
    pub text_color: String,
    pub needle_color: String,
    pub border_color: String,
    pub font_size: u32,
    pub font_family: String,
}

/// Indicator styling
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IndicatorStyle {
    pub color: String,
    pub background_color: String,
    pub border_color: String,
    pub font_size: u32,
    pub font_family: String,
}

/// Graph styling
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GraphStyle {
    pub background_color: String,
    pub grid_color: String,
    pub line_colors: Vec<String>,
    pub line_width: u32,
    pub font_size: u32,
    pub font_family: String,
}

/// Dashboard parser
pub struct DashboardParser;

impl DashboardParser {
    /// Parse a dashboard file
    pub fn parse<P: AsRef<Path>>(path: P) -> Result<Dashboard> {
        let content = std::fs::read_to_string(path)?;
        Self::parse_content(&content)
    }

    /// Parse dashboard content from string
    pub fn parse_content(content: &str) -> Result<Dashboard> {
        // Try to parse as JSON first (modern format)
        if let Ok(dashboard) = serde_json::from_str::<Dashboard>(content) {
            return Ok(dashboard);
        }

        // Fall back to legacy INI-style format
        Self::parse_legacy_format(content)
    }

    /// Parse legacy INI-style dashboard format
    fn parse_legacy_format(content: &str) -> Result<Dashboard> {
        let mut dashboard = Dashboard {
            metadata: DashMetadata {
                name: "Legacy Dashboard".to_string(),
                version: "1.0".to_string(),
                author: "Unknown".to_string(),
                description: "Imported from legacy format".to_string(),
                created: chrono::Utc::now().to_rfc3339(),
                modified: chrono::Utc::now().to_rfc3339(),
            },
            gauges: Vec::new(),
            indicators: Vec::new(),
            graphs: Vec::new(),
            layout: DashLayout {
                width: 800,
                height: 600,
                background_color: "#000000".to_string(),
                background_image: None,
                grid_enabled: false,
                grid_size: 10,
            },
        };

        let mut current_section = String::new();

        for line in content.lines() {
            let line = line.trim();
            
            if line.is_empty() || line.starts_with(';') {
                continue;
            }

            if line.starts_with('[') && line.ends_with(']') {
                current_section = line[1..line.len()-1].to_string();
                continue;
            }

            match current_section.as_str() {
                "Gauges" => {
                    if let Some(gauge) = Self::parse_gauge_line(line)? {
                        dashboard.gauges.push(gauge);
                    }
                }
                "Indicators" => {
                    if let Some(indicator) = Self::parse_indicator_line(line)? {
                        dashboard.indicators.push(indicator);
                    }
                }
                "Layout" => {
                    Self::parse_layout_line(line, &mut dashboard.layout)?;
                }
                _ => {}
            }
        }

        Ok(dashboard)
    }

    /// Parse gauge definition line
    fn parse_gauge_line(line: &str) -> Result<Option<DashGauge>> {
        // Example: rpm = 100, 100, 150, 150, analog, 0, 8000, RPM
        let parts: Vec<&str> = line.split('=').collect();
        if parts.len() != 2 {
            return Ok(None);
        }

        let name = parts[0].trim().to_string();
        let params: Vec<&str> = parts[1].split(',').map(|s| s.trim()).collect();
        
        if params.len() < 8 {
            return Ok(None);
        }

        let x = params[0].parse::<u32>()?;
        let y = params[1].parse::<u32>()?;
        let width = params[2].parse::<u32>()?;
        let height = params[3].parse::<u32>()?;
        let gauge_type = match params[4] {
            "analog" => GaugeType::Analog,
            "digital" => GaugeType::Digital,
            "bar" => GaugeType::Bar,
            "led" => GaugeType::Led,
            _ => GaugeType::Digital,
        };
        let min_value = params[5].parse::<f64>()?;
        let max_value = params[6].parse::<f64>()?;
        let units = params[7].to_string();

        Ok(Some(DashGauge {
            id: format!("gauge_{}", name),
            name: name.clone(),
            variable: name,
            position: Position { x, y },
            size: Size { width, height },
            gauge_type,
            min_value,
            max_value,
            units,
            warning_ranges: Vec::new(),
            danger_ranges: Vec::new(),
            style: GaugeStyle {
                background_color: "#333333".to_string(),
                text_color: "#FFFFFF".to_string(),
                needle_color: "#FF0000".to_string(),
                border_color: "#666666".to_string(),
                font_size: 12,
                font_family: "Arial".to_string(),
            },
        }))
    }

    /// Parse indicator definition line
    fn parse_indicator_line(line: &str) -> Result<Option<DashIndicator>> {
        // Simplified indicator parsing
        let parts: Vec<&str> = line.split('=').collect();
        if parts.len() != 2 {
            return Ok(None);
        }

        let name = parts[0].trim().to_string();
        
        Ok(Some(DashIndicator {
            id: format!("indicator_{}", name),
            name: name.clone(),
            variable: name,
            position: Position { x: 0, y: 0 },
            size: Size { width: 50, height: 20 },
            indicator_type: IndicatorType::Light,
            conditions: Vec::new(),
            style: IndicatorStyle {
                color: "#FF0000".to_string(),
                background_color: "#000000".to_string(),
                border_color: "#666666".to_string(),
                font_size: 12,
                font_family: "Arial".to_string(),
            },
        }))
    }

    /// Parse layout configuration line
    fn parse_layout_line(line: &str, layout: &mut DashLayout) -> Result<()> {
        if let Some((key, value)) = Self::parse_key_value(line) {
            match key.as_str() {
                "width" => layout.width = value.parse()?,
                "height" => layout.height = value.parse()?,
                "background_color" => layout.background_color = value,
                "grid_enabled" => layout.grid_enabled = value.parse::<bool>().unwrap_or(false),
                "grid_size" => layout.grid_size = value.parse()?,
                _ => {}
            }
        }
        Ok(())
    }

    /// Parse key-value pair
    fn parse_key_value(line: &str) -> Option<(String, String)> {
        let parts: Vec<&str> = line.splitn(2, '=').collect();
        if parts.len() == 2 {
            Some((parts[0].trim().to_string(), parts[1].trim().to_string()))
        } else {
            None
        }
    }

    /// Save dashboard to file
    pub fn save<P: AsRef<Path>>(dashboard: &Dashboard, path: P) -> Result<()> {
        let content = serde_json::to_string_pretty(dashboard)?;
        std::fs::write(path, content)?;
        Ok(())
    }

    /// Convert dashboard to web-compatible format
    pub fn to_web_dashboard(dashboard: &Dashboard) -> Result<serde_json::Value> {
        Ok(serde_json::to_value(dashboard)?)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parse_gauge_line() {
        let line = "rpm = 100, 100, 150, 150, analog, 0, 8000, RPM";
        let gauge = DashboardParser::parse_gauge_line(line).unwrap().unwrap();
        
        assert_eq!(gauge.name, "rpm");
        assert_eq!(gauge.position.x, 100);
        assert_eq!(gauge.min_value, 0.0);
        assert_eq!(gauge.max_value, 8000.0);
        assert_eq!(gauge.units, "RPM");
    }

    #[test]
    fn test_dashboard_serialization() {
        let dashboard = Dashboard {
            metadata: DashMetadata {
                name: "Test Dashboard".to_string(),
                version: "1.0".to_string(),
                author: "Test".to_string(),
                description: "Test dashboard".to_string(),
                created: "2024-01-01T00:00:00Z".to_string(),
                modified: "2024-01-01T00:00:00Z".to_string(),
            },
            gauges: Vec::new(),
            indicators: Vec::new(),
            graphs: Vec::new(),
            layout: DashLayout {
                width: 800,
                height: 600,
                background_color: "#000000".to_string(),
                background_image: None,
                grid_enabled: false,
                grid_size: 10,
            },
        };

        let json = serde_json::to_string(&dashboard).unwrap();
        let parsed: Dashboard = serde_json::from_str(&json).unwrap();
        assert_eq!(parsed.metadata.name, "Test Dashboard");
    }
}
