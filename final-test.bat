@echo off
echo Final Compilation Test
echo ======================

echo.
echo Testing compilation with all fixes applied...
cargo check

if %errorlevel% neq 0 (
    echo.
    echo FAILED: There are still compilation errors.
    echo Please check the error messages above.
    echo.
    pause
    exit /b 1
)

echo.
echo SUCCESS: All compilation errors resolved!
echo.

echo Testing build process...
cargo build

if %errorlevel% neq 0 (
    echo.
    echo WARNING: Build failed, but check passed.
    echo This might be due to linking issues or missing system dependencies.
    echo.
    pause
    exit /b 1
)

echo.
echo EXCELLENT: Project builds successfully!
echo.
echo The Universal Tuning Software is ready to run!
echo.
echo Next steps:
echo 1. Run 'cargo run' to start the application
echo 2. Open http://localhost:8080 in your browser
echo 3. Test the torque management system
echo.

pause
