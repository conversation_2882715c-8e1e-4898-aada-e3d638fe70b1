# Rust
/target/
**/*.rs.bk
Cargo.lock

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Tuning files
*.msq
*.yaml
*.yml
*.json
*.ini
*.bin
*.hex

# Temporary files
tmp/
temp/
*.tmp

# Build artifacts
dist/
build/
out/

# WebAssembly
*.wasm
pkg/

# Database
*.db
*.sqlite
*.sqlite3

# Configuration files (may contain sensitive data)
config.toml
config.yaml
config.json

# Hardware-specific
*.dll
*.so
*.dylib

# Test artifacts
test-results/
screenshots/

# Documentation build
docs/_build/
site/
