[package]
name = "tuning-software"
version = "0.1.0"
edition = "2021"
description = "Universal tuning software with torque-centric architecture"
license = "MIT OR Apache-2.0"

[[bin]]
name = "tuning-software"
path = "src/main.rs"

[dependencies]
# Local dependencies
tuning-core = { path = "tuning-core" }
tuning-hal = { path = "tuning-hal" }
tuning-formats = { path = "tuning-formats" }
tuning-web = { path = "tuning-web" }

# Workspace dependencies
tokio = { workspace = true }
anyhow = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true }
clap = { workspace = true }

[workspace]
members = [
    "tuning-core",
    "tuning-hal",
    "tuning-formats",
    "tuning-web",
]
resolver = "2"

[workspace.dependencies]
# Core dependencies
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
serde_yaml = "0.9"
anyhow = "1.0"
thiserror = "1.0"
tracing = "0.1"
tracing-subscriber = "0.3"

# Async and networking
reqwest = { version = "0.11", features = ["json"] }
tungstenite = "0.20"
tokio-tungstenite = "0.20"

# Database and storage
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "sqlite", "chrono", "uuid"] }
parquet = "50.0"
arrow = "50.0"

# WebAssembly
wasm-bindgen = "0.2"
wasm-bindgen-futures = "0.4"
js-sys = "0.3"
web-sys = "0.3"

# Hardware communication
serialport = "4.2"

# Cryptography and security
ring = "0.17"
rustls = "0.21"

# Utilities
uuid = { version = "1.0", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }
clap = { version = "4.0", features = ["derive"] }

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"

[profile.dev]
opt-level = 0
debug = true
