//! # Tuning File Format Conversion
//! 
//! Universal conversion between tuning file formats including:
//! - MegaSquirt (.msq)
//! - YAML (.yaml/.yml)
//! - JSON (.json)
//! - TunerStudio (.ini)
//! - rusEFI (.yaml)

pub mod msq;
pub mod yaml;
pub mod json;
pub mod ini;
pub mod dash;
pub mod megalog;
pub mod tunerstudio;
pub mod converter;
pub mod validation;

use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::Path;
use chrono::{DateTime, Utc};

/// Universal tuning data structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TuningData {
    pub metadata: TuningMetadata,
    pub engine_config: EngineConfiguration,
    pub fuel_tables: HashMap<String, Table2D>,
    pub ignition_tables: HashMap<String, Table2D>,
    pub boost_tables: HashMap<String, Table2D>,
    pub scalar_values: HashMap<String, ScalarValue>,
    pub constants: HashMap<String, Constant>,
    pub settings: HashMap<String, Setting>,
}

impl TuningData {
    pub fn new() -> Self {
        Self {
            metadata: TuningMetadata::default(),
            engine_config: EngineConfiguration::default(),
            fuel_tables: HashMap::new(),
            ignition_tables: HashMap::new(),
            boost_tables: HashMap::new(),
            scalar_values: HashMap::new(),
            constants: HashMap::new(),
            settings: HashMap::new(),
        }
    }

    /// Load tuning data from file, auto-detecting format
    pub fn load_from_file<P: AsRef<Path>>(path: P) -> Result<Self> {
        let path = path.as_ref();
        let extension = path.extension()
            .and_then(|ext| ext.to_str())
            .unwrap_or("");

        match extension.to_lowercase().as_str() {
            "msq" => msq::MsqFormat::load(path),
            "yaml" | "yml" => yaml::YamlFormat::load(path),
            "json" => json::JsonFormat::load(path),
            "ini" => {
                // Check if it's a TunerStudio INI file
                let content = std::fs::read_to_string(path)?;
                if content.contains("[MegaTune]") || content.contains("[Constants]") {
                    let ts_ini = tunerstudio::TunerStudioParser::parse(path)?;
                    tunerstudio::TunerStudioParser::to_tuning_data(&ts_ini)
                } else {
                    ini::IniFormat::load(path)
                }
            }
            _ => Err(anyhow::anyhow!("Unsupported file format: {}", extension)),
        }
    }

    /// Save tuning data to file, format determined by extension
    pub fn save_to_file<P: AsRef<Path>>(&self, path: P) -> Result<()> {
        let path = path.as_ref();
        let extension = path.extension()
            .and_then(|ext| ext.to_str())
            .unwrap_or("");

        match extension.to_lowercase().as_str() {
            "msq" => msq::MsqFormat::save(self, path),
            "yaml" | "yml" => yaml::YamlFormat::save(self, path),
            "json" => json::JsonFormat::save(self, path),
            "ini" => ini::IniFormat::save(self, path),
            "dash" => {
                // Convert to dashboard format and save
                let dashboard = self.to_dashboard()?;
                dash::DashboardParser::save(&dashboard, path)
            }
            _ => Err(anyhow::anyhow!("Unsupported file format: {}", extension)),
        }
    }

    /// Convert to another format
    pub fn convert<P: AsRef<Path>>(&self, input_path: P, output_path: P) -> Result<()> {
        converter::FormatConverter::convert(input_path, output_path)
    }

    /// Validate tuning data integrity
    pub fn validate(&self) -> Result<validation::ValidationReport> {
        validation::TuningValidator::validate(self)
    }

    /// Merge with another tuning data set
    pub fn merge(&mut self, other: &TuningData) -> Result<()> {
        // Merge tables
        for (name, table) in &other.fuel_tables {
            self.fuel_tables.insert(name.clone(), table.clone());
        }
        for (name, table) in &other.ignition_tables {
            self.ignition_tables.insert(name.clone(), table.clone());
        }
        for (name, table) in &other.boost_tables {
            self.boost_tables.insert(name.clone(), table.clone());
        }

        // Merge scalar values
        for (name, value) in &other.scalar_values {
            self.scalar_values.insert(name.clone(), value.clone());
        }

        // Merge constants
        for (name, constant) in &other.constants {
            self.constants.insert(name.clone(), constant.clone());
        }

        // Merge settings
        for (name, setting) in &other.settings {
            self.settings.insert(name.clone(), setting.clone());
        }

        // Update metadata
        self.metadata.last_modified = Utc::now();
        self.metadata.version += 1;

        Ok(())
    }

    /// Convert tuning data to dashboard format
    pub fn to_dashboard(&self) -> Result<dash::Dashboard> {
        let mut dashboard = dash::Dashboard {
            metadata: dash::DashMetadata {
                name: format!("{} Dashboard", self.metadata.name),
                version: "1.0".to_string(),
                author: self.metadata.author.clone(),
                description: format!("Auto-generated dashboard for {}", self.metadata.name),
                created: chrono::Utc::now().to_rfc3339(),
                modified: chrono::Utc::now().to_rfc3339(),
            },
            gauges: Vec::new(),
            indicators: Vec::new(),
            graphs: Vec::new(),
            layout: dash::DashLayout {
                width: 1200,
                height: 800,
                background_color: "#1a1a1a".to_string(),
                background_image: None,
                grid_enabled: true,
                grid_size: 20,
            },
        };

        // Create gauges from scalar values
        let mut x = 50;
        let mut y = 50;
        for (name, scalar) in &self.scalar_values {
            if Self::is_gauge_worthy(name) {
                dashboard.gauges.push(dash::DashGauge {
                    id: format!("gauge_{}", name),
                    name: scalar.name.clone(),
                    variable: name.clone(),
                    position: dash::Position { x, y },
                    size: dash::Size { width: 150, height: 150 },
                    gauge_type: Self::determine_gauge_type(name),
                    min_value: scalar.min_value.unwrap_or(0.0),
                    max_value: scalar.max_value.unwrap_or(100.0),
                    units: scalar.units.clone(),
                    warning_ranges: Self::create_warning_ranges(name, scalar),
                    danger_ranges: Self::create_danger_ranges(name, scalar),
                    style: dash::GaugeStyle {
                        background_color: "#2a2a2a".to_string(),
                        text_color: "#ffffff".to_string(),
                        needle_color: "#ff4444".to_string(),
                        border_color: "#555555".to_string(),
                        font_size: 14,
                        font_family: "Arial".to_string(),
                    },
                });

                x += 170;
                if x > 1000 {
                    x = 50;
                    y += 170;
                }
            }
        }

        Ok(dashboard)
    }

    /// Check if a parameter should have a gauge
    fn is_gauge_worthy(name: &str) -> bool {
        let name_lower = name.to_lowercase();
        name_lower.contains("rpm") ||
        name_lower.contains("temp") ||
        name_lower.contains("pressure") ||
        name_lower.contains("voltage") ||
        name_lower.contains("speed") ||
        name_lower.contains("throttle") ||
        name_lower.contains("afr") ||
        name_lower.contains("lambda") ||
        name_lower.contains("boost") ||
        name_lower.contains("map")
    }

    /// Determine appropriate gauge type
    fn determine_gauge_type(name: &str) -> dash::GaugeType {
        let name_lower = name.to_lowercase();
        if name_lower.contains("rpm") || name_lower.contains("speed") {
            dash::GaugeType::Analog
        } else if name_lower.contains("temp") || name_lower.contains("pressure") {
            dash::GaugeType::Bar
        } else {
            dash::GaugeType::Digital
        }
    }

    /// Create warning ranges for parameters
    fn create_warning_ranges(name: &str, scalar: &ScalarValue) -> Vec<dash::ValueRange> {
        let name_lower = name.to_lowercase();
        let max = scalar.max_value.unwrap_or(100.0);

        if name_lower.contains("temp") {
            vec![dash::ValueRange {
                min: max * 0.8,
                max,
                color: "#ffaa00".to_string(),
            }]
        } else if name_lower.contains("rpm") {
            vec![dash::ValueRange {
                min: max * 0.9,
                max,
                color: "#ffaa00".to_string(),
            }]
        } else {
            Vec::new()
        }
    }

    /// Create danger ranges for parameters
    fn create_danger_ranges(name: &str, scalar: &ScalarValue) -> Vec<dash::ValueRange> {
        let name_lower = name.to_lowercase();
        let max = scalar.max_value.unwrap_or(100.0);

        if name_lower.contains("temp") {
            vec![dash::ValueRange {
                min: max * 0.95,
                max,
                color: "#ff0000".to_string(),
            }]
        } else if name_lower.contains("rpm") {
            vec![dash::ValueRange {
                min: max * 0.98,
                max,
                color: "#ff0000".to_string(),
            }]
        } else {
            Vec::new()
        }
    }

    /// Load MegaLog file
    pub fn load_megalog<P: AsRef<Path>>(path: P) -> Result<megalog::MegaLog> {
        megalog::MegaLogParser::parse(path)
    }
}

/// Tuning metadata
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TuningMetadata {
    pub name: String,
    pub description: String,
    pub author: String,
    pub version: u32,
    pub created: DateTime<Utc>,
    pub last_modified: DateTime<Utc>,
    pub source_format: String,
    pub target_ecu: String,
    pub engine_code: String,
    pub checksum: Option<String>,
}

impl Default for TuningMetadata {
    fn default() -> Self {
        let now = Utc::now();
        Self {
            name: "Untitled Tune".to_string(),
            description: String::new(),
            author: "Unknown".to_string(),
            version: 1,
            created: now,
            last_modified: now,
            source_format: "unknown".to_string(),
            target_ecu: "generic".to_string(),
            engine_code: "unknown".to_string(),
            checksum: None,
        }
    }
}

/// Engine configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EngineConfiguration {
    pub displacement: f64,
    pub cylinders: u8,
    pub compression_ratio: f64,
    pub fuel_type: String,
    pub aspiration: AspirationType,
    pub injection_type: InjectionType,
    pub ignition_type: IgnitionType,
}

impl Default for EngineConfiguration {
    fn default() -> Self {
        Self {
            displacement: 2.0,
            cylinders: 4,
            compression_ratio: 10.0,
            fuel_type: "gasoline".to_string(),
            aspiration: AspirationType::NaturallyAspirated,
            injection_type: InjectionType::PortInjection,
            ignition_type: IgnitionType::DistributorlessIgnition,
        }
    }
}

/// Aspiration types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum AspirationType {
    NaturallyAspirated,
    Turbocharged,
    Supercharged,
    TwinCharged,
}

/// Injection types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum InjectionType {
    PortInjection,
    DirectInjection,
    DualInjection,
}

/// Ignition types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum IgnitionType {
    DistributorIgnition,
    DistributorlessIgnition,
    CoilOnPlug,
}

/// 2D lookup table
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Table2D {
    pub name: String,
    pub description: String,
    pub x_axis: Axis,
    pub y_axis: Axis,
    pub data: Vec<Vec<f64>>,
    pub units: String,
    pub interpolation: InterpolationType,
}

impl Table2D {
    pub fn new(name: &str, x_size: usize, y_size: usize) -> Self {
        Self {
            name: name.to_string(),
            description: String::new(),
            x_axis: Axis::new("X", x_size),
            y_axis: Axis::new("Y", y_size),
            data: vec![vec![0.0; x_size]; y_size],
            units: String::new(),
            interpolation: InterpolationType::Linear,
        }
    }

    /// Get value at specific coordinates
    pub fn get_value(&self, x: usize, y: usize) -> Option<f64> {
        self.data.get(y)?.get(x).copied()
    }

    /// Set value at specific coordinates
    pub fn set_value(&mut self, x: usize, y: usize, value: f64) -> Result<()> {
        if let Some(row) = self.data.get_mut(y) {
            if let Some(cell) = row.get_mut(x) {
                *cell = value;
                Ok(())
            } else {
                Err(anyhow::anyhow!("X coordinate {} out of bounds", x))
            }
        } else {
            Err(anyhow::anyhow!("Y coordinate {} out of bounds", y))
        }
    }

    /// Interpolate value at given axis values
    pub fn interpolate(&self, x_value: f64, y_value: f64) -> Result<f64> {
        // Find surrounding indices
        let x_indices = self.x_axis.find_surrounding_indices(x_value)?;
        let y_indices = self.y_axis.find_surrounding_indices(y_value)?;

        match self.interpolation {
            InterpolationType::Linear => self.linear_interpolate(x_value, y_value, x_indices, y_indices),
            InterpolationType::Cubic => self.cubic_interpolate(x_value, y_value, x_indices, y_indices),
            InterpolationType::Nearest => self.nearest_interpolate(x_indices, y_indices),
        }
    }

    fn linear_interpolate(&self, x_value: f64, y_value: f64, x_indices: (usize, usize), y_indices: (usize, usize)) -> Result<f64> {
        let (x0, x1) = x_indices;
        let (y0, y1) = y_indices;

        let x0_val = self.x_axis.values[x0];
        let x1_val = self.x_axis.values[x1];
        let y0_val = self.y_axis.values[y0];
        let y1_val = self.y_axis.values[y1];

        let q00 = self.get_value(x0, y0).unwrap_or(0.0);
        let q01 = self.get_value(x0, y1).unwrap_or(0.0);
        let q10 = self.get_value(x1, y0).unwrap_or(0.0);
        let q11 = self.get_value(x1, y1).unwrap_or(0.0);

        // Bilinear interpolation
        let x_weight = if x1_val != x0_val { (x_value - x0_val) / (x1_val - x0_val) } else { 0.0 };
        let y_weight = if y1_val != y0_val { (y_value - y0_val) / (y1_val - y0_val) } else { 0.0 };

        let r0 = q00 * (1.0 - x_weight) + q10 * x_weight;
        let r1 = q01 * (1.0 - x_weight) + q11 * x_weight;

        Ok(r0 * (1.0 - y_weight) + r1 * y_weight)
    }

    fn cubic_interpolate(&self, _x_value: f64, _y_value: f64, x_indices: (usize, usize), y_indices: (usize, usize)) -> Result<f64> {
        // Simplified cubic interpolation - would need more complex implementation
        self.linear_interpolate(_x_value, _y_value, x_indices, y_indices)
    }

    fn nearest_interpolate(&self, x_indices: (usize, usize), y_indices: (usize, usize)) -> Result<f64> {
        let (x0, _x1) = x_indices;
        let (y0, _y1) = y_indices;
        Ok(self.get_value(x0, y0).unwrap_or(0.0))
    }
}

/// Table axis definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Axis {
    pub name: String,
    pub units: String,
    pub values: Vec<f64>,
    pub min_value: f64,
    pub max_value: f64,
}

impl Axis {
    pub fn new(name: &str, size: usize) -> Self {
        let values: Vec<f64> = (0..size).map(|i| i as f64).collect();
        let min_value = values.first().copied().unwrap_or(0.0);
        let max_value = values.last().copied().unwrap_or(0.0);

        Self {
            name: name.to_string(),
            units: String::new(),
            values,
            min_value,
            max_value,
        }
    }

    /// Find surrounding indices for interpolation
    pub fn find_surrounding_indices(&self, value: f64) -> Result<(usize, usize)> {
        if self.values.is_empty() {
            return Err(anyhow::anyhow!("Axis has no values"));
        }

        if value <= self.values[0] {
            return Ok((0, 0));
        }

        if value >= self.values[self.values.len() - 1] {
            let last = self.values.len() - 1;
            return Ok((last, last));
        }

        for i in 0..self.values.len() - 1 {
            if value >= self.values[i] && value <= self.values[i + 1] {
                return Ok((i, i + 1));
            }
        }

        Err(anyhow::anyhow!("Could not find surrounding indices for value {}", value))
    }
}

/// Interpolation types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum InterpolationType {
    Linear,
    Cubic,
    Nearest,
}

/// Scalar value
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScalarValue {
    pub name: String,
    pub value: f64,
    pub units: String,
    pub min_value: Option<f64>,
    pub max_value: Option<f64>,
    pub description: String,
}

/// Constant value
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Constant {
    pub name: String,
    pub value: ConstantValue,
    pub description: String,
}

/// Constant value types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ConstantValue {
    Integer(i64),
    Float(f64),
    String(String),
    Boolean(bool),
}

/// Setting value
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Setting {
    pub name: String,
    pub value: SettingValue,
    pub description: String,
    pub category: String,
}

/// Setting value types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SettingValue {
    Integer(i64),
    Float(f64),
    String(String),
    Boolean(bool),
    Enum { value: String, options: Vec<String> },
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_tuning_data_creation() {
        let data = TuningData::new();
        assert_eq!(data.metadata.name, "Untitled Tune");
        assert!(data.fuel_tables.is_empty());
    }

    #[test]
    fn test_table_2d_creation() {
        let mut table = Table2D::new("test_table", 10, 8);
        assert_eq!(table.name, "test_table");
        assert_eq!(table.data.len(), 8);
        assert_eq!(table.data[0].len(), 10);

        // Test setting and getting values
        table.set_value(5, 3, 42.0).unwrap();
        assert_eq!(table.get_value(5, 3), Some(42.0));
    }

    #[test]
    fn test_axis_interpolation() {
        let mut axis = Axis::new("test", 5);
        axis.values = vec![0.0, 10.0, 20.0, 30.0, 40.0];

        let indices = axis.find_surrounding_indices(15.0).unwrap();
        assert_eq!(indices, (1, 2));

        let indices = axis.find_surrounding_indices(5.0).unwrap();
        assert_eq!(indices, (0, 1));
    }

    #[test]
    fn test_table_interpolation() {
        let mut table = Table2D::new("test", 3, 3);
        table.x_axis.values = vec![0.0, 10.0, 20.0];
        table.y_axis.values = vec![0.0, 10.0, 20.0];
        
        // Set corner values
        table.set_value(0, 0, 0.0).unwrap();
        table.set_value(1, 0, 10.0).unwrap();
        table.set_value(0, 1, 5.0).unwrap();
        table.set_value(1, 1, 15.0).unwrap();

        let interpolated = table.interpolate(5.0, 5.0).unwrap();
        assert!((interpolated - 7.5).abs() < 0.1);
    }
}
