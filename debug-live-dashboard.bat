@echo off
echo Universal Tuning Software - Debug Live Dashboard
echo =================================================

echo.
echo 🐛 DEBUGGING LIVE DASHBOARD ISSUES
echo.

echo ✅ FIXES APPLIED:
echo.
echo 🔧 Data Flow Debugging:
echo   ✓ Added console.log statements for gauge updates
echo   ✓ Added gauge element detection logging
echo   ✓ Added data source tracking (real vs simulated)
echo   ✓ Added selected gauges storage (window.currentSelectedGauges)
echo   ✓ Enhanced getSelectedGauges() with fallback logic
echo.
echo 🎛️ Dashboard Improvements:
echo   ✓ Auto-start Speeduino simulation when dashboard shown
echo   ✓ Force immediate update after dashboard creation
echo   ✓ Added test button for manual gauge testing
echo   ✓ Improved gauge element ID consistency
echo   ✓ Enhanced error handling and logging
echo.
echo 🧪 Testing Features:
echo   ✓ Test button in dashboard header
echo   ✓ Manual test data injection
echo   ✓ Console debugging output
echo   ✓ Real-time status monitoring
echo   ✓ Data source indicators
echo.

echo 🚀 DEBUGGING STEPS:
echo.
echo 1. Start application and open browser:
echo    cargo run
echo    http://localhost:8080
echo.
echo 2. Open browser developer tools:
echo    Press F12 or right-click → Inspect
echo    Go to Console tab
echo.
echo 3. Load dashboard file:
echo    - Click "Load File"
echo    - Select any .dash file
echo    - Watch console for parsing messages
echo.
echo 4. Select gauges:
echo    - Choose gauges from checkbox list
echo    - Click "Show Selected Gauges"
echo    - Watch console for gauge creation messages
echo.
echo 5. Debug dashboard:
echo    - Look for "Looking for gauge main_gauge_X: FOUND/NOT FOUND"
echo    - Check "Updating X gauges: [gauge names]"
echo    - Watch "Gauge [name]: [value] (real/simulated)"
echo.
echo 6. Test manually:
echo    - Click "🧪 Test" button in dashboard
echo    - Should see test data applied immediately
echo    - Check console for test messages
echo.

echo 🔍 CONSOLE DEBUG MESSAGES:
echo.
echo ✅ What to Look For:
echo   ✓ "Updating X gauges: [array of gauge names]"
echo   ✓ "Looking for gauge main_gauge_0: FOUND"
echo   ✓ "Gauge RPM: 3500 (simulated)"
echo   ✓ "Speeduino data stream started (100ms intervals)"
echo   ✓ "Dashboard showing X live gauges"
echo.
echo ❌ Problem Indicators:
echo   ✗ "Looking for gauge main_gauge_0: NOT FOUND"
echo   ✗ "No selected gauges found for update"
echo   ✗ "Gauge [name]: null (simulated)"
echo   ✗ No update messages in console
echo   ✗ Gauges showing "--" instead of numbers
echo.

echo 🧪 MANUAL TESTING:
echo.
echo Test Button Function:
echo   ✓ Click "🧪 Test" in dashboard header
echo   ✓ Injects test data: RPM=3500, MAP=120, TPS=45
echo   ✓ Should see immediate gauge updates
echo   ✓ Console shows "Test data applied"
echo   ✓ Gauges should show test values
echo.
echo Expected Behavior:
echo   ✓ Gauges display numbers instead of "--"
echo   ✓ Progress bars show percentage fill
echo   ✓ Colors change based on values
echo   ✓ Values update smoothly
echo   ✓ Console shows regular update messages
echo.

echo 🔧 TROUBLESHOOTING GUIDE:
echo.
echo Problem: Gauges show "--" (no data)
echo Solution:
echo   1. Check console for "NOT FOUND" messages
echo   2. Verify gauge IDs are correct (main_gauge_0, main_gauge_1, etc.)
echo   3. Click "🧪 Test" button to inject test data
echo   4. Check if Speeduino simulation is running
echo.
echo Problem: No console messages
echo Solution:
echo   1. Ensure browser dev tools are open
echo   2. Refresh page and try again
echo   3. Check if JavaScript errors are blocking execution
echo   4. Verify dashboard is actually visible
echo.
echo Problem: Gauges found but no values
echo Solution:
echo   1. Check if realTimeData object has values
echo   2. Verify getRealTimeValue() function is working
echo   3. Test with manual data injection
echo   4. Check gauge name matching logic
echo.
echo Problem: Values not updating
echo Solution:
echo   1. Verify update intervals are running
echo   2. Check if mainDashboardInterval is set
echo   3. Ensure updateMainDashboardWithRealData() is called
echo   4. Test Speeduino simulation is generating data
echo.

echo 📊 EXPECTED CONSOLE OUTPUT:
echo.
echo When Working Correctly:
echo.
echo [INFO] Dashboard showing 4 live gauges
echo Updating 4 gauges: ["RPM", "MAP", "TPS", "AFR"]
echo Looking for gauge main_gauge_0: FOUND
echo Looking for gauge main_gauge_1: FOUND
echo Looking for gauge main_gauge_2: FOUND
echo Looking for gauge main_gauge_3: FOUND
echo Gauge RPM: 2847 (simulated)
echo Gauge MAP: 125.3 (simulated)
echo Gauge TPS: 42.1 (simulated)
echo Gauge AFR: 14.8 (simulated)
echo [INFO] Speeduino data stream started (100ms intervals)
echo.

echo 🎯 QUICK FIX CHECKLIST:
echo.
echo ✅ Dashboard appears at top of page
echo ✅ Gauges show proper names
echo ✅ Click "🧪 Test" button works
echo ✅ Console shows "FOUND" for all gauges
echo ✅ Console shows gauge values updating
echo ✅ Progress bars fill with data
echo ✅ Colors change (blue/yellow/red)
echo ✅ Values update every few seconds
echo.

echo 🔄 IF STILL NOT WORKING:
echo.
echo 1. Refresh browser page (F5)
echo 2. Clear browser cache (Ctrl+Shift+R)
echo 3. Try different dashboard file
echo 4. Check browser console for errors
echo 5. Restart application (cargo run)
echo 6. Try different browser
echo.

echo ✨ DASHBOARD SHOULD NOW BE LIVE!
echo.
echo With these debugging tools you can:
echo 🔍 See exactly what's happening in console
echo 🧪 Test dashboard manually with test button
echo 📊 Monitor real-time data flow
echo 🎛️ Verify gauge creation and updates
echo 🚨 Identify specific issues quickly
echo.

echo The dashboard should show live, updating values
echo with smooth animations and color changes!
echo.

pause
