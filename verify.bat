@echo off
echo Universal Tuning Software - Final Verification
echo ================================================

echo.
echo Verifying all issues are resolved...
cargo check

if %errorlevel% neq 0 (
    echo FAILED: There are still compilation issues
    pause
    exit /b 1
)

echo.
echo SUCCESS: All compilation issues resolved!
echo.
echo The Universal Tuning Software is ready to run!
echo.
echo Features implemented:
echo ✓ Torque-centric architecture with arbitration
echo ✓ Safety monitoring system
echo ✓ Hardware abstraction layer (HAL)
echo ✓ Universal file format conversion
echo ✓ Modern web interface with WebSocket
echo ✓ Real-time data streaming
echo ✓ Mock device for Windows testing
echo.
echo Ready to start? (Y/N)
set /p choice="Enter choice: "

if /i "%choice%"=="Y" (
    echo.
    echo Starting Universal Tuning Software...
    echo Open http://localhost:8080 in your browser
    echo.
    cargo run
) else (
    echo.
    echo To start later, run: cargo run
    echo Then open: http://localhost:8080
)

pause
