{"metadata": {"name": "Racing Dashboard", "version": "1.0", "author": "Universal Tuning Software", "description": "High-performance racing dashboard with essential gauges", "created": "2024-01-01T00:00:00Z", "modified": "2024-01-01T00:00:00Z"}, "layout": {"width": 1200, "height": 800, "background_color": "#000000", "background_image": null, "grid_enabled": true, "grid_size": 20}, "gauges": [{"id": "gauge_rpm", "name": "RPM", "variable": "rpm", "position": {"x": 50, "y": 50}, "size": {"width": 200, "height": 200}, "gauge_type": "Analog", "min_value": 0, "max_value": 8000, "units": "RPM", "warning_ranges": [{"min": 7000, "max": 7500, "color": "#ffaa00"}], "danger_ranges": [{"min": 7500, "max": 8000, "color": "#ff0000"}], "style": {"background_color": "#1a1a1a", "text_color": "#ffffff", "needle_color": "#ff4444", "border_color": "#555555", "font_size": 16, "font_family": "<PERSON><PERSON>"}}, {"id": "gauge_map", "name": "MAP", "variable": "map", "position": {"x": 300, "y": 50}, "size": {"width": 150, "height": 150}, "gauge_type": "Bar", "min_value": 0, "max_value": 300, "units": "kPa", "warning_ranges": [{"min": 250, "max": 280, "color": "#ffaa00"}], "danger_ranges": [{"min": 280, "max": 300, "color": "#ff0000"}], "style": {"background_color": "#1a1a1a", "text_color": "#ffffff", "needle_color": "#44ff44", "border_color": "#555555", "font_size": 14, "font_family": "<PERSON><PERSON>"}}, {"id": "gauge_boost", "name": "Boost", "variable": "boost", "position": {"x": 500, "y": 50}, "size": {"width": 150, "height": 150}, "gauge_type": "Analog", "min_value": -1, "max_value": 2, "units": "bar", "warning_ranges": [{"min": 1.5, "max": 1.8, "color": "#ffaa00"}], "danger_ranges": [{"min": 1.8, "max": 2.0, "color": "#ff0000"}], "style": {"background_color": "#1a1a1a", "text_color": "#ffffff", "needle_color": "#4444ff", "border_color": "#555555", "font_size": 14, "font_family": "<PERSON><PERSON>"}}, {"id": "gauge_afr", "name": "AFR", "variable": "afr", "position": {"x": 700, "y": 50}, "size": {"width": 150, "height": 150}, "gauge_type": "Digital", "min_value": 10, "max_value": 20, "units": "AFR", "warning_ranges": [{"min": 10, "max": 12, "color": "#ffaa00"}, {"min": 16, "max": 18, "color": "#ffaa00"}], "danger_ranges": [{"min": 18, "max": 20, "color": "#ff0000"}], "style": {"background_color": "#1a1a1a", "text_color": "#ffffff", "needle_color": "#ffff44", "border_color": "#555555", "font_size": 18, "font_family": "<PERSON><PERSON>"}}, {"id": "gauge_egt", "name": "EGT", "variable": "egt", "position": {"x": 900, "y": 50}, "size": {"width": 150, "height": 150}, "gauge_type": "Bar", "min_value": 0, "max_value": 1000, "units": "°C", "warning_ranges": [{"min": 850, "max": 900, "color": "#ffaa00"}], "danger_ranges": [{"min": 900, "max": 1000, "color": "#ff0000"}], "style": {"background_color": "#1a1a1a", "text_color": "#ffffff", "needle_color": "#ff8844", "border_color": "#555555", "font_size": 14, "font_family": "<PERSON><PERSON>"}}, {"id": "gauge_coolant_temp", "name": "Coolant <PERSON>", "variable": "coolant_temp", "position": {"x": 50, "y": 300}, "size": {"width": 120, "height": 120}, "gauge_type": "Digital", "min_value": 0, "max_value": 120, "units": "°C", "warning_ranges": [{"min": 95, "max": 105, "color": "#ffaa00"}], "danger_ranges": [{"min": 105, "max": 120, "color": "#ff0000"}], "style": {"background_color": "#1a1a1a", "text_color": "#ffffff", "needle_color": "#44ffff", "border_color": "#555555", "font_size": 12, "font_family": "<PERSON><PERSON>"}}, {"id": "gauge_oil_pressure", "name": "Oil Pressure", "variable": "oil_pressure", "position": {"x": 200, "y": 300}, "size": {"width": 120, "height": 120}, "gauge_type": "Bar", "min_value": 0, "max_value": 10, "units": "bar", "warning_ranges": [{"min": 0, "max": 2, "color": "#ffaa00"}], "danger_ranges": [{"min": 0, "max": 1, "color": "#ff0000"}], "style": {"background_color": "#1a1a1a", "text_color": "#ffffff", "needle_color": "#ff44ff", "border_color": "#555555", "font_size": 12, "font_family": "<PERSON><PERSON>"}}, {"id": "gauge_fuel_pressure", "name": "Fuel Pressure", "variable": "fuel_pressure", "position": {"x": 350, "y": 300}, "size": {"width": 120, "height": 120}, "gauge_type": "Digital", "min_value": 0, "max_value": 5, "units": "bar", "warning_ranges": [{"min": 0, "max": 2.5, "color": "#ffaa00"}], "danger_ranges": [{"min": 0, "max": 2, "color": "#ff0000"}], "style": {"background_color": "#1a1a1a", "text_color": "#ffffff", "needle_color": "#88ff44", "border_color": "#555555", "font_size": 12, "font_family": "<PERSON><PERSON>"}}], "indicators": [{"id": "indicator_shift_light", "name": "Shift Light", "variable": "rpm", "position": {"x": 550, "y": 250}, "size": {"width": 100, "height": 50}, "indicator_type": "Light", "conditions": [{"condition_type": "GreaterThan", "value": 7000, "action": {"Show": null}}], "style": {"color": "#ff0000", "background_color": "#000000", "border_color": "#555555", "font_size": 14, "font_family": "<PERSON><PERSON>"}}, {"id": "indicator_check_engine", "name": "Check Engine", "variable": "engine_fault", "position": {"x": 700, "y": 250}, "size": {"width": 100, "height": 50}, "indicator_type": "Icon", "conditions": [{"condition_type": "Equal", "value": 1, "action": {"Blink": null}}], "style": {"color": "#ffaa00", "background_color": "#000000", "border_color": "#555555", "font_size": 12, "font_family": "<PERSON><PERSON>"}}], "graphs": [{"id": "graph_power_curve", "name": "Power Curve", "variables": ["rpm", "torque", "power"], "position": {"x": 50, "y": 500}, "size": {"width": 500, "height": 250}, "graph_type": "Line", "time_window": 30.0, "y_min": null, "y_max": null, "style": {"background_color": "#1a1a1a", "grid_color": "#333333", "line_colors": ["#ff4444", "#44ff44", "#4444ff"], "line_width": 2, "font_size": 10, "font_family": "<PERSON><PERSON>"}}, {"id": "graph_afr_trend", "name": "AFR Trend", "variables": ["afr", "lambda"], "position": {"x": 600, "y": 500}, "size": {"width": 400, "height": 250}, "graph_type": "Line", "time_window": 60.0, "y_min": 10.0, "y_max": 18.0, "style": {"background_color": "#1a1a1a", "grid_color": "#333333", "line_colors": ["#ffff44", "#ff8844"], "line_width": 2, "font_size": 10, "font_family": "<PERSON><PERSON>"}}]}