@echo off
echo Universal Tuning Software - Fix and Test
echo ==========================================

echo.
echo Fixed duplicate regex dependency in tuning-formats/Cargo.toml
echo.

echo Testing compilation after fix...
cargo check

if %errorlevel% neq 0 (
    echo FAILED: Still has compilation errors
    echo.
    echo Please check the error messages above.
    pause
    exit /b 1
)

echo.
echo SUCCESS: All compilation errors fixed!
echo.

echo Testing build...
cargo build

if %errorlevel% neq 0 (
    echo FAILED: Build errors occurred
    pause
    exit /b 1
)

echo.
echo SUCCESS: Build completed successfully!
echo.

echo ===================================================
echo ENHANCED UNIVERSAL TUNING SOFTWARE IS READY!
echo ===================================================
echo.
echo 🎯 All new features are now available:
echo.
echo ✓ TunerStudio INI file parsing
echo ✓ Dashboard file support (.dash)
echo ✓ MegaLog viewer (.msl, .csv)
echo ✓ Enhanced format conversion
echo ✓ Interactive web interface
echo ✓ Real-time torque management
echo ✓ Safety monitoring system
echo.
echo Ready to start the application? (Y/N)
set /p choice="Enter choice: "

if /i "%choice%"=="Y" (
    echo.
    echo Starting Universal Tuning Software...
    echo Open http://localhost:8080 in your browser
    echo.
    cargo run
) else (
    echo.
    echo To start later, run: cargo run
    echo Then open: http://localhost:8080
)

pause
