//! # Tuning Web Interface
//! 
//! Web-based interface for the tuning software with both server and WebAssembly support.

#[cfg(feature = "server")]
pub mod server;

#[cfg(feature = "wasm")]
pub mod wasm;

pub mod api;
pub mod websocket;

use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// Web API request structure
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ApiRequest {
    pub id: String,
    pub method: String,
    pub params: HashMap<String, serde_json::Value>,
}

/// Web API response structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiResponse {
    pub id: String,
    pub success: bool,
    pub data: Option<serde_json::Value>,
    pub error: Option<String>,
}

impl ApiResponse {
    pub fn success(id: String, data: serde_json::Value) -> Self {
        Self {
            id,
            success: true,
            data: Some(data),
            error: None,
        }
    }

    pub fn error(id: String, error: String) -> Self {
        Self {
            id,
            success: false,
            data: None,
            error: Some(error),
        }
    }
}

/// WebSocket message types
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum WebSocketMessage {
    /// Engine status update
    EngineStatus {
        status: tuning_core::EngineStatus,
    },
    /// Torque request
    TorqueRequest {
        request: tuning_core::TuningRequest,
    },
    /// Torque response
    TorqueResponse {
        response: tuning_core::TuningResponse,
    },
    /// Safety alert
    SafetyAlert {
        alert: tuning_core::safety::SafetyAlert,
    },
    /// Diagnostics update
    DiagnosticsUpdate {
        summary: tuning_core::diagnostics::DiagnosticsSummary,
    },
    /// Device connection status
    DeviceStatus {
        device_name: String,
        connected: bool,
    },
    /// Real-time data stream
    RealTimeData {
        timestamp: chrono::DateTime<chrono::Utc>,
        data: HashMap<String, f64>,
    },
    /// Error message
    Error {
        message: String,
    },
}

/// Configuration for the web interface
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WebConfig {
    pub host: String,
    pub port: u16,
    pub static_files_path: String,
    pub enable_cors: bool,
    pub websocket_path: String,
    pub api_path: String,
}

impl Default for WebConfig {
    fn default() -> Self {
        Self {
            host: "127.0.0.1".to_string(),
            port: 8080,
            static_files_path: "./web".to_string(),
            enable_cors: true,
            websocket_path: "/ws".to_string(),
            api_path: "/api".to_string(),
        }
    }
}

/// Web application state
#[derive(Debug)]
pub struct AppState {
    pub tuning_engine: std::sync::Arc<tokio::sync::Mutex<tuning_core::TuningEngine>>,
    pub device_manager: std::sync::Arc<tokio::sync::Mutex<tuning_hal::DeviceManager>>,
    pub websocket_clients: std::sync::Arc<tokio::sync::RwLock<HashMap<String, WebSocketClient>>>,
}

impl AppState {
    pub fn new(
        tuning_engine: tuning_core::TuningEngine,
        device_manager: tuning_hal::DeviceManager,
    ) -> Self {
        Self {
            tuning_engine: std::sync::Arc::new(tokio::sync::Mutex::new(tuning_engine)),
            device_manager: std::sync::Arc::new(tokio::sync::Mutex::new(device_manager)),
            websocket_clients: std::sync::Arc::new(tokio::sync::RwLock::new(HashMap::new())),
        }
    }

    /// Broadcast message to all connected WebSocket clients
    pub async fn broadcast_message(&self, message: &WebSocketMessage) -> anyhow::Result<()> {
        let clients = self.websocket_clients.read().await;
        let message_json = serde_json::to_string(message)?;

        for client in clients.values() {
            if let Err(e) = client.send_message(&message_json).await {
                tracing::warn!("Failed to send message to client {}: {}", client.id, e);
            }
        }

        Ok(())
    }

    /// Add WebSocket client
    pub async fn add_websocket_client(&self, client: WebSocketClient) {
        let mut clients = self.websocket_clients.write().await;
        clients.insert(client.id.clone(), client);
    }

    /// Remove WebSocket client
    pub async fn remove_websocket_client(&self, client_id: &str) {
        let mut clients = self.websocket_clients.write().await;
        clients.remove(client_id);
    }

    /// Get current engine status
    pub async fn get_engine_status(&self) -> anyhow::Result<tuning_core::EngineStatus> {
        let engine = self.tuning_engine.lock().await;
        Ok(engine.get_status())
    }

    /// Process tuning request
    pub async fn process_tuning_request(
        &self,
        request: tuning_core::TuningRequest,
    ) -> anyhow::Result<tuning_core::TuningResponse> {
        let mut engine = self.tuning_engine.lock().await;
        engine.process_tuning_request(request).await
    }
}

/// WebSocket client representation
#[derive(Debug)]
pub struct WebSocketClient {
    pub id: String,
    pub sender: tokio::sync::mpsc::UnboundedSender<String>,
    pub connected_at: chrono::DateTime<chrono::Utc>,
}

impl WebSocketClient {
    pub fn new(id: String, sender: tokio::sync::mpsc::UnboundedSender<String>) -> Self {
        Self {
            id,
            sender,
            connected_at: chrono::Utc::now(),
        }
    }

    pub async fn send_message(&self, message: &str) -> anyhow::Result<()> {
        self.sender
            .send(message.to_string())
            .map_err(|e| anyhow::anyhow!("Failed to send message: {}", e))?;
        Ok(())
    }
}

/// Real-time data collector
#[derive(Debug)]
pub struct RealTimeDataCollector {
    app_state: std::sync::Arc<AppState>,
    collection_interval: std::time::Duration,
}

impl RealTimeDataCollector {
    pub fn new(app_state: std::sync::Arc<AppState>, interval_ms: u64) -> Self {
        Self {
            app_state,
            collection_interval: std::time::Duration::from_millis(interval_ms),
        }
    }

    /// Start collecting and broadcasting real-time data
    pub async fn start(&self) -> anyhow::Result<()> {
        let mut interval = tokio::time::interval(self.collection_interval);

        loop {
            interval.tick().await;

            // Collect current engine status
            if let Ok(status) = self.app_state.get_engine_status().await {
                let message = WebSocketMessage::EngineStatus { status };
                
                if let Err(e) = self.app_state.broadcast_message(&message).await {
                    tracing::error!("Failed to broadcast engine status: {}", e);
                }
            }

            // Collect real-time sensor data (simulated for now)
            let mut data = HashMap::new();
            data.insert("rpm".to_string(), 2500.0);
            data.insert("throttle".to_string(), 25.0);
            data.insert("map".to_string(), 1.2);
            data.insert("coolant_temp".to_string(), 85.0);
            data.insert("lambda".to_string(), 0.98);

            let rt_message = WebSocketMessage::RealTimeData {
                timestamp: chrono::Utc::now(),
                data,
            };

            if let Err(e) = self.app_state.broadcast_message(&rt_message).await {
                tracing::error!("Failed to broadcast real-time data: {}", e);
            }
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_api_response_creation() {
        let success_response = ApiResponse::success(
            "test_id".to_string(),
            serde_json::json!({"result": "ok"}),
        );
        assert!(success_response.success);
        assert!(success_response.data.is_some());
        assert!(success_response.error.is_none());

        let error_response = ApiResponse::error(
            "test_id".to_string(),
            "Test error".to_string(),
        );
        assert!(!error_response.success);
        assert!(error_response.data.is_none());
        assert!(error_response.error.is_some());
    }

    #[test]
    fn test_websocket_message_serialization() {
        let message = WebSocketMessage::Error {
            message: "Test error".to_string(),
        };

        let serialized = serde_json::to_string(&message).unwrap();
        let deserialized: WebSocketMessage = serde_json::from_str(&serialized).unwrap();

        match deserialized {
            WebSocketMessage::Error { message } => {
                assert_eq!(message, "Test error");
            }
            _ => panic!("Wrong message type"),
        }
    }

    #[test]
    fn test_web_config_default() {
        let config = WebConfig::default();
        assert_eq!(config.host, "127.0.0.1");
        assert_eq!(config.port, 8080);
        assert!(config.enable_cors);
    }
}
