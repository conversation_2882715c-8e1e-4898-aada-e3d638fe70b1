//! # Diagnostics Engine
//! 
//! Comprehensive diagnostics and monitoring system for tuning operations.

use anyhow::Result;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};
use std::collections::HashMap;
use uuid::Uuid;
use crate::{TuningRequest, torque::TorqueOutput};

/// Diagnostics engine for monitoring and analysis
#[derive(Debug)]
pub struct DiagnosticsEngine {
    operations_log: Vec<OperationRecord>,
    fault_codes: HashMap<u16, FaultCode>,
    performance_metrics: PerformanceMetrics,
    config: DiagnosticsConfig,
}

impl DiagnosticsEngine {
    pub fn new() -> Result<Self> {
        Ok(Self {
            operations_log: Vec::new(),
            fault_codes: HashMap::new(),
            performance_metrics: PerformanceMetrics::default(),
            config: DiagnosticsConfig::default(),
        })
    }

    /// Record a tuning operation for diagnostics
    pub async fn record_operation(&mut self, request: &TuningRequest, response: &TorqueOutput) -> Result<()> {
        let record = OperationRecord {
            id: Uuid::new_v4(),
            timestamp: Utc::now(),
            request_summary: RequestSummary {
                torque_demand: request.torque_demand.driver_demand,
                engine_speed: request.torque_demand.engine_speed,
                throttle_position: request.torque_demand.throttle_position,
            },
            response_summary: ResponseSummary {
                final_torque: response.requested_torque,
                fuel_pulse_width: response.fuel_delivery.pulse_width,
                ignition_advance: response.ignition_timing.advance_degrees,
            },
            processing_time_ms: 0.0, // Would be calculated in real implementation
            success: true,
        };

        self.operations_log.push(record);

        // Trim log if it gets too large
        if self.operations_log.len() > self.config.max_log_entries {
            self.operations_log.remove(0);
        }

        // Update performance metrics
        self.update_performance_metrics(&record).await?;

        Ok(())
    }

    /// Update performance metrics based on operation
    async fn update_performance_metrics(&mut self, record: &OperationRecord) -> Result<()> {
        self.performance_metrics.total_operations += 1;
        self.performance_metrics.average_processing_time = 
            (self.performance_metrics.average_processing_time * (self.performance_metrics.total_operations - 1) as f64 + 
             record.processing_time_ms) / self.performance_metrics.total_operations as f64;

        if record.success {
            self.performance_metrics.successful_operations += 1;
        } else {
            self.performance_metrics.failed_operations += 1;
        }

        self.performance_metrics.last_update = Utc::now();
        Ok(())
    }

    /// Add a fault code
    pub fn add_fault_code(&mut self, code: u16, description: String, severity: FaultSeverity) {
        let fault = FaultCode {
            code,
            description,
            severity,
            first_occurrence: Utc::now(),
            last_occurrence: Utc::now(),
            occurrence_count: 1,
            active: true,
        };

        if let Some(existing) = self.fault_codes.get_mut(&code) {
            existing.last_occurrence = Utc::now();
            existing.occurrence_count += 1;
            existing.active = true;
        } else {
            self.fault_codes.insert(code, fault);
        }
    }

    /// Clear a fault code
    pub fn clear_fault_code(&mut self, code: u16) -> Result<()> {
        if let Some(fault) = self.fault_codes.get_mut(&code) {
            fault.active = false;
            Ok(())
        } else {
            Err(anyhow::anyhow!("Fault code {} not found", code))
        }
    }

    /// Get diagnostics summary
    pub fn get_summary(&self) -> DiagnosticsSummary {
        let active_faults: Vec<_> = self.fault_codes
            .values()
            .filter(|f| f.active)
            .cloned()
            .collect();

        let recent_operations: Vec<_> = self.operations_log
            .iter()
            .rev()
            .take(10)
            .cloned()
            .collect();

        DiagnosticsSummary {
            active_fault_codes: active_faults,
            recent_operations,
            performance_metrics: self.performance_metrics.clone(),
            system_health: self.calculate_system_health(),
            last_update: Utc::now(),
        }
    }

    /// Calculate overall system health score (0-100)
    fn calculate_system_health(&self) -> f64 {
        let mut health_score = 100.0;

        // Reduce score based on active faults
        for fault in self.fault_codes.values().filter(|f| f.active) {
            match fault.severity {
                FaultSeverity::Critical => health_score -= 30.0,
                FaultSeverity::Major => health_score -= 15.0,
                FaultSeverity::Minor => health_score -= 5.0,
                FaultSeverity::Info => health_score -= 1.0,
            }
        }

        // Reduce score based on failure rate
        if self.performance_metrics.total_operations > 0 {
            let failure_rate = self.performance_metrics.failed_operations as f64 / 
                              self.performance_metrics.total_operations as f64;
            health_score -= failure_rate * 20.0;
        }

        health_score.max(0.0).min(100.0)
    }

    /// Get detailed fault information
    pub fn get_fault_details(&self, code: u16) -> Option<&FaultCode> {
        self.fault_codes.get(&code)
    }

    /// Export diagnostics data for analysis
    pub fn export_diagnostics(&self) -> DiagnosticsExport {
        DiagnosticsExport {
            timestamp: Utc::now(),
            operations_log: self.operations_log.clone(),
            fault_codes: self.fault_codes.clone(),
            performance_metrics: self.performance_metrics.clone(),
        }
    }
}

/// Diagnostics configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DiagnosticsConfig {
    pub max_log_entries: usize,
    pub enable_detailed_logging: bool,
    pub auto_clear_old_faults: bool,
    pub fault_retention_days: u32,
}

impl Default for DiagnosticsConfig {
    fn default() -> Self {
        Self {
            max_log_entries: 1000,
            enable_detailed_logging: true,
            auto_clear_old_faults: true,
            fault_retention_days: 30,
        }
    }
}

/// Operation record for diagnostics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct OperationRecord {
    pub id: Uuid,
    pub timestamp: DateTime<Utc>,
    pub request_summary: RequestSummary,
    pub response_summary: ResponseSummary,
    pub processing_time_ms: f64,
    pub success: bool,
}

/// Request summary for logging
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RequestSummary {
    pub torque_demand: f64,
    pub engine_speed: f64,
    pub throttle_position: f64,
}

/// Response summary for logging
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ResponseSummary {
    pub final_torque: f64,
    pub fuel_pulse_width: f64,
    pub ignition_advance: f64,
}

/// Fault code structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FaultCode {
    pub code: u16,
    pub description: String,
    pub severity: FaultSeverity,
    pub first_occurrence: DateTime<Utc>,
    pub last_occurrence: DateTime<Utc>,
    pub occurrence_count: u32,
    pub active: bool,
}

/// Fault severity levels
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FaultSeverity {
    Critical,
    Major,
    Minor,
    Info,
}

/// Performance metrics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMetrics {
    pub total_operations: u64,
    pub successful_operations: u64,
    pub failed_operations: u64,
    pub average_processing_time: f64,
    pub last_update: DateTime<Utc>,
}

impl Default for PerformanceMetrics {
    fn default() -> Self {
        Self {
            total_operations: 0,
            successful_operations: 0,
            failed_operations: 0,
            average_processing_time: 0.0,
            last_update: Utc::now(),
        }
    }
}

/// Diagnostics summary
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DiagnosticsSummary {
    pub active_fault_codes: Vec<FaultCode>,
    pub recent_operations: Vec<OperationRecord>,
    pub performance_metrics: PerformanceMetrics,
    pub system_health: f64,
    pub last_update: DateTime<Utc>,
}

/// Diagnostics export structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DiagnosticsExport {
    pub timestamp: DateTime<Utc>,
    pub operations_log: Vec<OperationRecord>,
    pub fault_codes: HashMap<u16, FaultCode>,
    pub performance_metrics: PerformanceMetrics,
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::torque::{TorqueDemand, TorqueOutput, FuelDelivery, IgnitionTiming};

    #[tokio::test]
    async fn test_diagnostics_engine_creation() {
        let engine = DiagnosticsEngine::new();
        assert!(engine.is_ok());
    }

    #[tokio::test]
    async fn test_operation_recording() {
        let mut engine = DiagnosticsEngine::new().unwrap();
        
        let request = TuningRequest {
            id: Uuid::new_v4(),
            timestamp: Utc::now(),
            torque_demand: TorqueDemand::default(),
            engine_parameters: HashMap::new(),
            safety_overrides: None,
        };

        let response = TorqueOutput {
            requested_torque: 100.0,
            fuel_delivery: FuelDelivery {
                pulse_width: 5.0,
                injector_duty_cycle: 25.0,
                fuel_pressure: 3.0,
            },
            ignition_timing: IgnitionTiming {
                advance_degrees: 15.0,
                dwell_time: 3.0,
            },
            timestamp: Utc::now(),
            arbitration_reason: None,
        };

        let result = engine.record_operation(&request, &response).await;
        assert!(result.is_ok());
        assert_eq!(engine.operations_log.len(), 1);
    }

    #[test]
    fn test_fault_code_management() {
        let mut engine = DiagnosticsEngine::new().unwrap();
        
        engine.add_fault_code(100, "Test fault".to_string(), FaultSeverity::Minor);
        assert!(engine.fault_codes.contains_key(&100));
        
        let result = engine.clear_fault_code(100);
        assert!(result.is_ok());
        assert!(!engine.fault_codes.get(&100).unwrap().active);
    }
}
