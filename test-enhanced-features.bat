@echo off
echo Universal Tuning Software - Enhanced Features Test
echo ===================================================

echo.
echo Testing enhanced features compilation...
cargo check --quiet

if %errorlevel% neq 0 (
    echo FAILED: Enhanced features compilation errors
    pause
    exit /b 1
)

echo SUCCESS: Enhanced features compile successfully!
echo.

echo Testing build with new features...
cargo build --quiet

if %errorlevel% neq 0 (
    echo FAILED: Enhanced features build errors
    pause
    exit /b 1
)

echo SUCCESS: Enhanced features build completed!
echo.

echo ===================================================
echo ENHANCED UNIVERSAL TUNING SOFTWARE READY!
echo ===================================================
echo.
echo 🎯 NEW FEATURES AVAILABLE:
echo.
echo 📁 TunerStudio Integration:
echo   ✓ Complete INI file parsing
echo   ✓ Constants, tables, and gauge definitions
echo   ✓ Dialog and menu structure support
echo   ✓ Bi-directional conversion to universal format
echo.
echo 🎛️ Dashboard System:
echo   ✓ Custom gauge layouts (.dash files)
echo   ✓ Multiple gauge types (analog, digital, bar, LED)
echo   ✓ Warning and danger ranges with color coding
echo   ✓ Real-time graphs and indicators
echo   ✓ Auto-generation from tuning data
echo.
echo 📊 MegaLog Viewer:
echo   ✓ .msl and .csv log file support
echo   ✓ Statistical analysis and visualization
echo   ✓ Time range selection and filtering
echo   ✓ Export capabilities between formats
echo   ✓ Real-time data plotting
echo.
echo 🔄 Enhanced Format Conversion:
echo   ✓ TunerStudio INI ↔ Universal formats
echo   ✓ Dashboard files ↔ JSON/YAML
echo   ✓ MegaLog ↔ CSV conversion
echo   ✓ Automatic format detection
echo.
echo 🌐 Web Interface Enhancements:
echo   ✓ File upload and parsing
echo   ✓ Interactive dashboard editor
echo   ✓ Real-time log viewer
echo   ✓ Format conversion tools
echo   ✓ Enhanced API endpoints
echo.
echo Example files available:
echo   - examples/megasquirt.ini (TunerStudio INI)
echo   - examples/racing_dashboard.dash (Dashboard)
echo   - examples/config.yaml (Engine configuration)
echo.
echo Ready to start? (Y/N)
set /p choice="Enter choice: "

if /i "%choice%"=="Y" (
    echo.
    echo Starting Enhanced Universal Tuning Software...
    echo.
    echo Web Interface: http://localhost:8080
    echo Features:
    echo   - Load TunerStudio INI files
    echo   - Create custom dashboards
    echo   - Analyze MegaLog data
    echo   - Convert between formats
    echo   - Real-time torque management
    echo.
    cargo run
) else (
    echo.
    echo To start later, run: cargo run
    echo Then open: http://localhost:8080
    echo.
    echo Try these features:
    echo 1. Click "Load File" and select examples/megasquirt.ini
    echo 2. Use "Dashboard Editor" to create custom layouts
    echo 3. Open "MegaLog Viewer" for data analysis
    echo 4. Test "Convert Format" for file conversion
)

pause
