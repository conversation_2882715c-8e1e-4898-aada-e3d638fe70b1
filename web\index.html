<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Universal Tuning Software</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card h3 {
            margin-bottom: 15px;
            color: #ffd700;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-connected {
            background-color: #4CAF50;
            box-shadow: 0 0 10px #4CAF50;
        }

        .status-disconnected {
            background-color: #f44336;
            box-shadow: 0 0 10px #f44336;
        }

        .metric {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .metric:last-child {
            border-bottom: none;
        }

        .metric-value {
            font-weight: bold;
            color: #ffd700;
        }

        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            text-transform: uppercase;
            font-weight: bold;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn-secondary {
            background: linear-gradient(45deg, #74b9ff, #0984e3);
        }

        .btn-success {
            background: linear-gradient(45deg, #00b894, #00a085);
        }

        .log-container {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
        }

        .log-entry {
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            margin-bottom: 5px;
            padding: 5px;
            border-left: 3px solid #ffd700;
            padding-left: 10px;
        }

        .log-error {
            border-left-color: #ff6b6b;
            color: #ff6b6b;
        }

        .log-warning {
            border-left-color: #fdcb6e;
            color: #fdcb6e;
        }

        .log-info {
            border-left-color: #74b9ff;
            color: #74b9ff;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .dashboard {
                grid-template-columns: 1fr;
            }
            
            .controls {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏎️ Universal Tuning Software</h1>
            <p>Torque-Centric Architecture • Universal Compatibility • Hardware Agnostic</p>
        </div>

        <div class="dashboard">
            <div class="card">
                <h3>🔧 Engine Status</h3>
                <div class="metric">
                    <span>Connection:</span>
                    <span><span id="connection-status" class="status-indicator status-disconnected"></span><span id="connection-text">Disconnected</span></span>
                </div>
                <div class="metric">
                    <span>RPM:</span>
                    <span class="metric-value" id="rpm">0</span>
                </div>
                <div class="metric">
                    <span>Torque:</span>
                    <span class="metric-value" id="torque">0 Nm</span>
                </div>
                <div class="metric">
                    <span>Throttle:</span>
                    <span class="metric-value" id="throttle">0%</span>
                </div>
                <div class="metric">
                    <span>MAP:</span>
                    <span class="metric-value" id="map">0.0 bar</span>
                </div>
            </div>

            <div class="card">
                <h3>🛡️ Safety Monitor</h3>
                <div class="metric">
                    <span>System Armed:</span>
                    <span class="metric-value" id="safety-armed">Yes</span>
                </div>
                <div class="metric">
                    <span>Active Alerts:</span>
                    <span class="metric-value" id="active-alerts">0</span>
                </div>
                <div class="metric">
                    <span>Max Torque:</span>
                    <span class="metric-value" id="max-torque">500 Nm</span>
                </div>
                <div class="metric">
                    <span>Max RPM:</span>
                    <span class="metric-value" id="max-rpm">7000</span>
                </div>
            </div>

            <div class="card">
                <h3>📊 Performance</h3>
                <div class="metric">
                    <span>Operations:</span>
                    <span class="metric-value" id="total-operations">0</span>
                </div>
                <div class="metric">
                    <span>Success Rate:</span>
                    <span class="metric-value" id="success-rate">100%</span>
                </div>
                <div class="metric">
                    <span>Avg Response:</span>
                    <span class="metric-value" id="avg-response">0.0ms</span>
                </div>
                <div class="metric">
                    <span>System Health:</span>
                    <span class="metric-value" id="system-health">100%</span>
                </div>
            </div>

            <div class="card">
                <h3>🔌 Device Info</h3>
                <div class="metric">
                    <span>Device Type:</span>
                    <span class="metric-value" id="device-type">Mock</span>
                </div>
                <div class="metric">
                    <span>Protocol:</span>
                    <span class="metric-value" id="protocol">CAN</span>
                </div>
                <div class="metric">
                    <span>Baud Rate:</span>
                    <span class="metric-value" id="baud-rate">500000</span>
                </div>
                <div class="metric">
                    <span>Buffer Size:</span>
                    <span class="metric-value" id="buffer-size">4096</span>
                </div>
            </div>
        </div>

        <div class="controls">
            <button class="btn btn-success" onclick="connectDevice()">Connect Device</button>
            <button class="btn btn-secondary" onclick="disconnectDevice()">Disconnect</button>
            <button class="btn" onclick="sendTestRequest()">Send Test Request</button>
            <button class="btn btn-secondary" onclick="clearLogs()">Clear Logs</button>
        </div>

        <div class="log-container">
            <h3>📝 System Log</h3>
            <div id="log-entries">
                <div class="log-entry log-info">[INFO] System initialized</div>
                <div class="log-entry log-info">[INFO] Web interface loaded</div>
                <div class="log-entry log-warning">[WARN] No device connected</div>
            </div>
        </div>
    </div>

    <script>
        let ws = null;
        let isConnected = false;

        // Initialize WebSocket connection
        function initWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws`;
            
            ws = new WebSocket(wsUrl);
            
            ws.onopen = function() {
                addLogEntry('WebSocket connected', 'info');
                updateConnectionStatus(true);
            };
            
            ws.onmessage = function(event) {
                try {
                    const message = JSON.parse(event.data);
                    handleWebSocketMessage(message);
                } catch (e) {
                    addLogEntry(`WebSocket message error: ${e.message}`, 'error');
                }
            };
            
            ws.onclose = function() {
                addLogEntry('WebSocket disconnected', 'warning');
                updateConnectionStatus(false);
                // Attempt to reconnect after 3 seconds
                setTimeout(initWebSocket, 3000);
            };
            
            ws.onerror = function(error) {
                addLogEntry(`WebSocket error: ${error}`, 'error');
            };
        }

        // Handle WebSocket messages
        function handleWebSocketMessage(message) {
            switch (message.type) {
                case 'EngineStatus':
                    updateEngineStatus(message.status);
                    break;
                case 'RealTimeData':
                    updateRealTimeData(message.data);
                    break;
                case 'SafetyAlert':
                    handleSafetyAlert(message.alert);
                    break;
                case 'DiagnosticsUpdate':
                    updateDiagnostics(message.summary);
                    break;
                case 'DeviceStatus':
                    updateDeviceStatus(message.device_name, message.connected);
                    break;
                case 'Error':
                    addLogEntry(`Error: ${message.message}`, 'error');
                    break;
            }
        }

        // Update engine status display
        function updateEngineStatus(status) {
            // Update torque status
            if (status.torque_status) {
                // Update UI with torque status
            }
            
            // Update safety status
            if (status.safety_status) {
                document.getElementById('safety-armed').textContent = status.safety_status.system_armed ? 'Yes' : 'No';
                document.getElementById('active-alerts').textContent = status.safety_status.active_alerts.length;
            }
            
            // Update diagnostics
            if (status.diagnostics_summary) {
                updateDiagnostics(status.diagnostics_summary);
            }
        }

        // Update real-time data
        function updateRealTimeData(data) {
            if (data.rpm) document.getElementById('rpm').textContent = Math.round(data.rpm);
            if (data.throttle) document.getElementById('throttle').textContent = Math.round(data.throttle) + '%';
            if (data.map) document.getElementById('map').textContent = data.map.toFixed(2) + ' bar';
        }

        // Update diagnostics display
        function updateDiagnostics(summary) {
            if (summary.performance_metrics) {
                const metrics = summary.performance_metrics;
                document.getElementById('total-operations').textContent = metrics.total_operations;
                
                const successRate = metrics.total_operations > 0 
                    ? ((metrics.successful_operations / metrics.total_operations) * 100).toFixed(1)
                    : '100';
                document.getElementById('success-rate').textContent = successRate + '%';
                
                document.getElementById('avg-response').textContent = metrics.average_processing_time.toFixed(1) + 'ms';
            }
            
            if (summary.system_health !== undefined) {
                document.getElementById('system-health').textContent = Math.round(summary.system_health) + '%';
            }
        }

        // Handle safety alerts
        function handleSafetyAlert(alert) {
            addLogEntry(`SAFETY ALERT: ${alert.message}`, 'error');
        }

        // Update device status
        function updateDeviceStatus(deviceName, connected) {
            updateConnectionStatus(connected);
            addLogEntry(`Device ${deviceName}: ${connected ? 'Connected' : 'Disconnected'}`, connected ? 'info' : 'warning');
        }

        // Update connection status indicator
        function updateConnectionStatus(connected) {
            isConnected = connected;
            const indicator = document.getElementById('connection-status');
            const text = document.getElementById('connection-text');
            
            if (connected) {
                indicator.className = 'status-indicator status-connected';
                text.textContent = 'Connected';
            } else {
                indicator.className = 'status-indicator status-disconnected';
                text.textContent = 'Disconnected';
            }
        }

        // Add log entry
        function addLogEntry(message, type = 'info') {
            const logEntries = document.getElementById('log-entries');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            
            logEntries.appendChild(entry);
            logEntries.scrollTop = logEntries.scrollHeight;
            
            // Keep only last 100 entries
            while (logEntries.children.length > 100) {
                logEntries.removeChild(logEntries.firstChild);
            }
        }

        // Control functions
        function connectDevice() {
            addLogEntry('Attempting to connect device...', 'info');
            // Send connect command via WebSocket or API
        }

        function disconnectDevice() {
            addLogEntry('Disconnecting device...', 'info');
            // Send disconnect command via WebSocket or API
        }

        function sendTestRequest() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                const testRequest = {
                    type: 'TorqueRequest',
                    request: {
                        id: crypto.randomUUID(),
                        timestamp: new Date().toISOString(),
                        torque_demand: {
                            driver_demand: 200.0,
                            system_request: 0.0,
                            safety_limits: {
                                max_torque: 500.0,
                                max_rpm: 7000.0,
                                max_boost: 2.0,
                                max_egt: 950.0,
                                emergency_cutoff: false
                            },
                            engine_speed: 3000.0,
                            throttle_position: 40.0,
                            load_factor: 0.5
                        },
                        engine_parameters: {},
                        safety_overrides: null
                    }
                };
                
                ws.send(JSON.stringify(testRequest));
                addLogEntry('Test torque request sent', 'info');
            } else {
                addLogEntry('WebSocket not connected', 'error');
            }
        }

        function clearLogs() {
            document.getElementById('log-entries').innerHTML = '';
            addLogEntry('Logs cleared', 'info');
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initWebSocket();
            addLogEntry('Universal Tuning Software initialized', 'info');
        });
    </script>
</body>
</html>
