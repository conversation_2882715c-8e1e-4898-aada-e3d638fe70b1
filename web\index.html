<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Universal Tuning Software</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card h3 {
            margin-bottom: 15px;
            color: #ffd700;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-connected {
            background-color: #4CAF50;
            box-shadow: 0 0 10px #4CAF50;
        }

        .status-disconnected {
            background-color: #f44336;
            box-shadow: 0 0 10px #f44336;
        }

        .metric {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .metric:last-child {
            border-bottom: none;
        }

        .metric-value {
            font-weight: bold;
            color: #ffd700;
        }

        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
            text-transform: uppercase;
            font-weight: bold;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn-secondary {
            background: linear-gradient(45deg, #74b9ff, #0984e3);
        }

        .btn-success {
            background: linear-gradient(45deg, #00b894, #00a085);
        }

        .log-container {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
        }

        .log-entry {
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            margin-bottom: 5px;
            padding: 5px;
            border-left: 3px solid #ffd700;
            padding-left: 10px;
        }

        .log-error {
            border-left-color: #ff6b6b;
            color: #ff6b6b;
        }

        .log-warning {
            border-left-color: #fdcb6e;
            color: #fdcb6e;
        }

        .log-info {
            border-left-color: #74b9ff;
            color: #74b9ff;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .dashboard {
                grid-template-columns: 1fr;
            }
            
            .controls {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏎️ Universal Tuning Software</h1>
            <p>Torque-Centric Architecture • Universal Compatibility • Hardware Agnostic</p>
        </div>

        <div class="dashboard">
            <div class="card">
                <h3>🔧 Engine Status</h3>
                <div class="metric">
                    <span>Connection:</span>
                    <span><span id="connection-status" class="status-indicator status-disconnected"></span><span id="connection-text">Disconnected</span></span>
                </div>
                <div class="metric">
                    <span>RPM:</span>
                    <span class="metric-value" id="rpm">0</span>
                </div>
                <div class="metric">
                    <span>Torque:</span>
                    <span class="metric-value" id="torque">0 Nm</span>
                </div>
                <div class="metric">
                    <span>Throttle:</span>
                    <span class="metric-value" id="throttle">0%</span>
                </div>
                <div class="metric">
                    <span>MAP:</span>
                    <span class="metric-value" id="map">0.0 bar</span>
                </div>
            </div>

            <div class="card">
                <h3>🛡️ Safety Monitor</h3>
                <div class="metric">
                    <span>System Armed:</span>
                    <span class="metric-value" id="safety-armed">Yes</span>
                </div>
                <div class="metric">
                    <span>Active Alerts:</span>
                    <span class="metric-value" id="active-alerts">0</span>
                </div>
                <div class="metric">
                    <span>Max Torque:</span>
                    <span class="metric-value" id="max-torque">500 Nm</span>
                </div>
                <div class="metric">
                    <span>Max RPM:</span>
                    <span class="metric-value" id="max-rpm">7000</span>
                </div>
            </div>

            <div class="card">
                <h3>📊 Performance</h3>
                <div class="metric">
                    <span>Operations:</span>
                    <span class="metric-value" id="total-operations">0</span>
                </div>
                <div class="metric">
                    <span>Success Rate:</span>
                    <span class="metric-value" id="success-rate">100%</span>
                </div>
                <div class="metric">
                    <span>Avg Response:</span>
                    <span class="metric-value" id="avg-response">0.0ms</span>
                </div>
                <div class="metric">
                    <span>System Health:</span>
                    <span class="metric-value" id="system-health">100%</span>
                </div>
            </div>

            <div class="card">
                <h3>🔌 Device Info</h3>
                <div class="metric">
                    <span>Device Type:</span>
                    <span class="metric-value" id="device-type">Mock</span>
                </div>
                <div class="metric">
                    <span>Protocol:</span>
                    <span class="metric-value" id="protocol">CAN</span>
                </div>
                <div class="metric">
                    <span>Baud Rate:</span>
                    <span class="metric-value" id="baud-rate">500000</span>
                </div>
                <div class="metric">
                    <span>Buffer Size:</span>
                    <span class="metric-value" id="buffer-size">4096</span>
                </div>
            </div>
        </div>

        <div class="controls">
            <button class="btn btn-success" onclick="connectDevice()">Connect Device</button>
            <button class="btn btn-secondary" onclick="disconnectDevice()">Disconnect</button>
            <button class="btn" onclick="sendTestRequest()">Send Test Request</button>
            <button class="btn btn-secondary" onclick="clearLogs()">Clear Logs</button>
        </div>

        <div class="card">
            <h3>📁 File Operations</h3>
            <div class="controls">
                <input type="file" id="fileInput" accept=".ini,.dash,.msl,.csv,.msq,.yaml,.json" style="display: none;" onchange="handleFileUpload(event)">
                <button class="btn btn-secondary" onclick="document.getElementById('fileInput').click()">Load File</button>
                <button class="btn" onclick="showFormatConverter()">Convert Format</button>
                <button class="btn btn-secondary" onclick="showDashboardEditor()">Dashboard Editor</button>
                <button class="btn" onclick="showMegaLogViewer()">MegaLog Viewer</button>
            </div>
            <div style="margin-top: 15px;">
                <div style="color: #74b9ff; font-size: 0.9rem;">
                    <div>📄 Supported: .ini (TunerStudio), .dash (Dashboard), .msl/.csv (MegaLog)</div>
                    <div>🔄 Formats: .msq, .yaml, .json for conversion</div>
                </div>
            </div>
        </div>

        <div class="log-container">
            <h3>📝 System Log</h3>
            <div id="log-entries">
                <div class="log-entry log-info">[INFO] System initialized</div>
                <div class="log-entry log-info">[INFO] Web interface loaded</div>
                <div class="log-entry log-warning">[WARN] No device connected</div>
            </div>
        </div>
    </div>

    <script>
        let ws = null;
        let isConnected = false;

        // Initialize WebSocket connection
        function initWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws`;
            
            ws = new WebSocket(wsUrl);
            
            ws.onopen = function() {
                addLogEntry('WebSocket connected', 'info');
                updateConnectionStatus(true);
            };
            
            ws.onmessage = function(event) {
                try {
                    const message = JSON.parse(event.data);
                    handleWebSocketMessage(message);
                } catch (e) {
                    addLogEntry(`WebSocket message error: ${e.message}`, 'error');
                }
            };
            
            ws.onclose = function() {
                addLogEntry('WebSocket disconnected', 'warning');
                updateConnectionStatus(false);
                // Attempt to reconnect after 3 seconds
                setTimeout(initWebSocket, 3000);
            };
            
            ws.onerror = function(error) {
                addLogEntry(`WebSocket error: ${error}`, 'error');
            };
        }

        // Handle WebSocket messages
        function handleWebSocketMessage(message) {
            switch (message.type) {
                case 'EngineStatus':
                    updateEngineStatus(message.status);
                    break;
                case 'RealTimeData':
                    updateRealTimeData(message.data);
                    break;
                case 'SafetyAlert':
                    handleSafetyAlert(message.alert);
                    break;
                case 'DiagnosticsUpdate':
                    updateDiagnostics(message.summary);
                    break;
                case 'DeviceStatus':
                    updateDeviceStatus(message.device_name, message.connected);
                    break;
                case 'Error':
                    addLogEntry(`Error: ${message.message}`, 'error');
                    break;
            }
        }

        // Update engine status display
        function updateEngineStatus(status) {
            // Update torque status
            if (status.torque_status) {
                // Update UI with torque status
            }
            
            // Update safety status
            if (status.safety_status) {
                document.getElementById('safety-armed').textContent = status.safety_status.system_armed ? 'Yes' : 'No';
                document.getElementById('active-alerts').textContent = status.safety_status.active_alerts.length;
            }
            
            // Update diagnostics
            if (status.diagnostics_summary) {
                updateDiagnostics(status.diagnostics_summary);
            }
        }

        // Update real-time data
        function updateRealTimeData(data) {
            if (data.rpm) document.getElementById('rpm').textContent = Math.round(data.rpm);
            if (data.throttle) document.getElementById('throttle').textContent = Math.round(data.throttle) + '%';
            if (data.map) document.getElementById('map').textContent = data.map.toFixed(2) + ' bar';
        }

        // Update diagnostics display
        function updateDiagnostics(summary) {
            if (summary.performance_metrics) {
                const metrics = summary.performance_metrics;
                document.getElementById('total-operations').textContent = metrics.total_operations;
                
                const successRate = metrics.total_operations > 0 
                    ? ((metrics.successful_operations / metrics.total_operations) * 100).toFixed(1)
                    : '100';
                document.getElementById('success-rate').textContent = successRate + '%';
                
                document.getElementById('avg-response').textContent = metrics.average_processing_time.toFixed(1) + 'ms';
            }
            
            if (summary.system_health !== undefined) {
                document.getElementById('system-health').textContent = Math.round(summary.system_health) + '%';
            }
        }

        // Handle safety alerts
        function handleSafetyAlert(alert) {
            addLogEntry(`SAFETY ALERT: ${alert.message}`, 'error');
        }

        // Update device status
        function updateDeviceStatus(deviceName, connected) {
            updateConnectionStatus(connected);
            addLogEntry(`Device ${deviceName}: ${connected ? 'Connected' : 'Disconnected'}`, connected ? 'info' : 'warning');
        }

        // Update connection status indicator
        function updateConnectionStatus(connected) {
            isConnected = connected;
            const indicator = document.getElementById('connection-status');
            const text = document.getElementById('connection-text');
            
            if (connected) {
                indicator.className = 'status-indicator status-connected';
                text.textContent = 'Connected';
            } else {
                indicator.className = 'status-indicator status-disconnected';
                text.textContent = 'Disconnected';
            }
        }

        // Add log entry
        function addLogEntry(message, type = 'info') {
            const logEntries = document.getElementById('log-entries');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            
            logEntries.appendChild(entry);
            logEntries.scrollTop = logEntries.scrollHeight;
            
            // Keep only last 100 entries
            while (logEntries.children.length > 100) {
                logEntries.removeChild(logEntries.firstChild);
            }
        }

        // Control functions
        function connectDevice() {
            addLogEntry('Attempting to connect device...', 'info');
            // Send connect command via WebSocket or API
        }

        function disconnectDevice() {
            addLogEntry('Disconnecting device...', 'info');
            // Send disconnect command via WebSocket or API
        }

        function sendTestRequest() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                const testRequest = {
                    type: 'TorqueRequest',
                    request: {
                        id: crypto.randomUUID(),
                        timestamp: new Date().toISOString(),
                        torque_demand: {
                            driver_demand: 200.0,
                            system_request: 0.0,
                            safety_limits: {
                                max_torque: 500.0,
                                max_rpm: 7000.0,
                                max_boost: 2.0,
                                max_egt: 950.0,
                                emergency_cutoff: false
                            },
                            engine_speed: 3000.0,
                            throttle_position: 40.0,
                            load_factor: 0.5
                        },
                        engine_parameters: {},
                        safety_overrides: null
                    }
                };
                
                ws.send(JSON.stringify(testRequest));
                addLogEntry('Test torque request sent', 'info');
            } else {
                addLogEntry('WebSocket not connected', 'error');
            }
        }

        function clearLogs() {
            document.getElementById('log-entries').innerHTML = '';
            addLogEntry('Logs cleared', 'info');
        }

        // File handling functions
        function handleFileUpload(event) {
            const file = event.target.files[0];
            if (!file) {
                addLogEntry(`❌ No file selected`, 'error');
                return;
            }

            const fileName = file.name;
            const extension = fileName.split('.').pop().toLowerCase();

            addLogEntry(`📁 Starting file upload: ${fileName}`, 'info');
            addLogEntry(`🔍 File extension: ${extension}`, 'info');
            addLogEntry(`📊 File size: ${file.size} bytes`, 'info');
            addLogEntry(`📄 File type: ${file.type}`, 'info');

            // Create a FileReader to read the file
            const reader = new FileReader();

            reader.onload = function(e) {
                addLogEntry(`✅ File read successfully`, 'info');
                const content = e.target.result;
                addLogEntry(`📄 Content type: ${typeof content}`, 'info');
                addLogEntry(`📊 Content length: ${content ? content.length : 0}`, 'info');

                try {
                    processUploadedFile(fileName, extension, content, file);
                } catch (error) {
                    addLogEntry(`❌ Error processing file: ${error.message}`, 'error');
                    console.error('File processing error:', error);
                }
            };

            reader.onerror = function(error) {
                addLogEntry(`❌ Error reading file: ${fileName}`, 'error');
                addLogEntry(`❌ Reader error: ${error}`, 'error');
                console.error('FileReader error:', error);
            };

            reader.onprogress = function(e) {
                if (e.lengthComputable) {
                    const percent = Math.round((e.loaded / e.total) * 100);
                    addLogEntry(`📊 Reading file: ${percent}%`, 'info');
                }
            };

            // Read file based on type
            try {
                if (extension === 'msl') {
                    addLogEntry(`🔄 Reading as binary (MSL)...`, 'info');
                    reader.readAsArrayBuffer(file);
                } else {
                    addLogEntry(`🔄 Reading as text...`, 'info');
                    reader.readAsText(file);
                }
            } catch (error) {
                addLogEntry(`❌ Error starting file read: ${error.message}`, 'error');
                console.error('FileReader start error:', error);
            }
        }

        function processUploadedFile(fileName, extension, content, file) {
            addLogEntry(`🔍 Processing ${extension.toUpperCase()} file: ${fileName}`, 'info');
            addLogEntry(`📊 File size: ${file.size} bytes`, 'info');
            addLogEntry(`📄 Content length: ${content ? content.length : 0} characters`, 'info');
            addLogEntry(`📄 Content type: ${typeof content}`, 'info');

            // Debug: Show first 100 characters of content
            if (content && typeof content === 'string') {
                const preview = content.substring(0, 100).replace(/\n/g, '\\n').replace(/\r/g, '\\r');
                addLogEntry(`👀 Content preview: "${preview}..."`, 'info');
            }

            try {
                switch (extension) {
                    case 'ini':
                        addLogEntry(`🎛️ Processing INI file...`, 'info');
                        if (typeof content === 'string' && (content.includes('[MegaTune]') || content.includes('[Constants]'))) {
                            addLogEntry(`🎯 TunerStudio INI detected`, 'info');
                            parseTunerStudioIni(fileName, content);
                        } else {
                            addLogEntry('📄 Standard INI file detected', 'info');
                            showFileContent(fileName, content, 'INI Configuration File');
                        }
                        break;

                    case 'dash':
                        addLogEntry(`🎛️ Dashboard file detected: ${fileName}`, 'info');
                        addLogEntry(`🔄 Calling loadDashboard function...`, 'info');
                        loadDashboard(fileName, content);
                        addLogEntry(`✅ loadDashboard function completed`, 'info');
                        break;

                    case 'msl':
                    case 'csv':
                        addLogEntry(`📊 Log file detected: ${fileName}`, 'info');
                        loadMegaLog(fileName, content, extension);
                        break;

                    case 'msq':
                    case 'yaml':
                    case 'json':
                        addLogEntry(`🔧 Tuning file detected: ${fileName}`, 'info');
                        loadTuningFile(fileName, content, extension);
                        break;

                    default:
                        addLogEntry(`❌ Unsupported file type: ${extension}`, 'error');
                        addLogEntry('📋 Supported: .ini, .dash, .msl, .csv, .msq, .yaml, .json', 'info');
                        addLogEntry('🔍 Showing file content for debugging...', 'info');
                        showFileContent(fileName, content, `Unknown File Type (.${extension})`);
                }
            } catch (error) {
                addLogEntry(`❌ Error in processUploadedFile: ${error.message}`, 'error');
                console.error('processUploadedFile error:', error);
                addLogEntry('🔍 Showing file content for debugging...', 'info');
                showFileContent(fileName, content, `Error Processing File (.${extension})`);
            }
        }

        function parseTunerStudioIni(fileName, content) {
            addLogEntry(`🎛️ Parsing TunerStudio INI: ${fileName}`, 'info');

            // Parse the INI content
            const sections = parseIniSections(content);

            // Count different sections
            const constants = sections['Constants'] || [];
            const tables = sections['Tables'] || [];
            const gauges = sections['Gauges'] || [];
            const dialogs = sections['Dialog'] || [];

            addLogEntry(`✅ TunerStudio INI parsed successfully!`, 'info');
            addLogEntry(`📊 Found ${constants.length} constants`, 'info');
            addLogEntry(`📋 Found ${tables.length} tables`, 'info');
            addLogEntry(`🎯 Found ${gauges.length} gauges`, 'info');
            addLogEntry(`🖥️ Found ${dialogs.length} dialogs`, 'info');

            // Show detailed view
            showTunerStudioData(sections, fileName);
        }

        function parseIniSections(content) {
            const sections = {};
            let currentSection = '';
            let currentSectionContent = [];

            const lines = content.split('\n');

            for (let line of lines) {
                line = line.trim();

                // Skip comments and empty lines
                if (!line || line.startsWith(';') || line.startsWith('#')) {
                    continue;
                }

                // Check for section headers
                if (line.startsWith('[') && line.endsWith(']')) {
                    // Save previous section
                    if (currentSection) {
                        sections[currentSection] = currentSectionContent;
                    }

                    // Start new section
                    currentSection = line.slice(1, -1);
                    currentSectionContent = [];
                } else if (currentSection) {
                    currentSectionContent.push(line);
                }
            }

            // Save last section
            if (currentSection) {
                sections[currentSection] = currentSectionContent;
            }

            return sections;
        }

        function loadDashboard(fileName, content) {
            addLogEntry(`🎛️ Loading TunerStudio dashboard: ${fileName}`, 'info');
            addLogEntry(`📄 Content type: ${typeof content}`, 'info');

            // Check if content is valid
            if (!content) {
                addLogEntry(`❌ Dashboard content is null or undefined`, 'error');
                return;
            }

            addLogEntry(`📄 File size: ${content.length} characters`, 'info');

            // Check if this is a TunerStudio .dash file (binary or text format)
            const isBinary = content.includes('\x00') || content.charCodeAt(0) < 32;
            const isJSON = content.trim().startsWith('{');

            if (isBinary) {
                addLogEntry(`🎛️ Detected TunerStudio binary dashboard format`, 'info');
                parseTunerStudioBinaryDash(fileName, content);
            } else if (isJSON) {
                addLogEntry(`📄 Detected JSON dashboard format`, 'info');
                parseJSONDashboard(fileName, content);
            } else {
                addLogEntry(`📄 Detected text-based dashboard format`, 'info');
                parseTunerStudioTextDash(fileName, content);
            }
        }

        function parseJSONDashboard(fileName, content) {
            try {
                const dashboard = JSON.parse(content);
                addLogEntry(`✅ JSON dashboard parsed successfully!`, 'info');
                addLogEntry(`🎯 ${dashboard.gauges?.length || 0} gauges configured`, 'info');
                showDashboardViewer(dashboard, fileName);
            } catch (e) {
                addLogEntry(`❌ JSON parse failed: ${e.message}`, 'error');
                showFileContent(fileName, content, 'Invalid JSON Dashboard');
            }
        }

        function parseTunerStudioBinaryDash(fileName, content) {
            addLogEntry(`🔍 Parsing TunerStudio binary dashboard...`, 'info');

            const dashboard = {
                metadata: {
                    name: fileName.replace('.dash', ''),
                    version: "TunerStudio",
                    author: "TunerStudio",
                    description: "Imported TunerStudio binary dashboard",
                    created: new Date().toISOString(),
                    modified: new Date().toISOString()
                },
                layout: {
                    width: 1024,
                    height: 768,
                    background_color: "#000000",
                    background_image: null,
                    grid_enabled: false,
                    grid_size: 20
                },
                gauges: [],
                indicators: [],
                graphs: []
            };

            // Enhanced TunerStudio binary parser
            const bytes = new Uint8Array(content.length);
            for (let i = 0; i < content.length; i++) {
                bytes[i] = content.charCodeAt(i);
            }

            // Look for text strings in the binary data (gauge names, variables)
            const extractedStrings = [];
            let currentString = '';

            for (let i = 0; i < bytes.length; i++) {
                const byte = bytes[i];

                // If it's a printable ASCII character
                if (byte >= 32 && byte <= 126) {
                    currentString += String.fromCharCode(byte);
                } else {
                    // End of string
                    if (currentString.length > 2) {
                        extractedStrings.push(currentString);
                    }
                    currentString = '';
                }
            }

            // Filter strings that look like gauge names or variables
            const gaugeStrings = extractedStrings.filter(str => {
                const lower = str.toLowerCase();
                return (
                    str.length >= 3 && str.length <= 30 &&
                    (lower.includes('rpm') || lower.includes('map') || lower.includes('tps') ||
                     lower.includes('afr') || lower.includes('temp') || lower.includes('pressure') ||
                     lower.includes('volt') || lower.includes('speed') || lower.includes('boost') ||
                     lower.includes('fuel') || lower.includes('ignition') || lower.includes('timing') ||
                     lower.includes('lambda') || lower.includes('ego') || lower.includes('baro') ||
                     /^[a-zA-Z][a-zA-Z0-9_]*$/.test(str)) // Valid variable name pattern
                );
            });

            addLogEntry(`📝 Extracted ${extractedStrings.length} text strings from binary`, 'info');
            addLogEntry(`🎯 Found ${gaugeStrings.length} potential gauge identifiers`, 'info');

            // Look for coordinate patterns in binary data
            const coordinates = [];
            for (let i = 0; i < bytes.length - 8; i += 4) {
                // Look for 4-byte sequences that could be coordinates
                const x = (bytes[i] | (bytes[i+1] << 8) | (bytes[i+2] << 16) | (bytes[i+3] << 24));
                const y = (bytes[i+4] | (bytes[i+5] << 8) | (bytes[i+6] << 16) | (bytes[i+7] << 24));

                // Check if these look like reasonable screen coordinates
                if (x >= 0 && x <= 2000 && y >= 0 && y <= 2000 && x % 10 === 0 && y % 10 === 0) {
                    coordinates.push({ x, y });
                }
            }

            addLogEntry(`📐 Found ${coordinates.length} potential coordinate pairs`, 'info');

            // Create gauges by combining strings and coordinates
            const maxGauges = Math.min(50, Math.max(gaugeStrings.length, coordinates.length));
            let gaugeCount = 0;

            for (let i = 0; i < maxGauges; i++) {
                const gaugeName = gaugeStrings[i] || `Gauge ${i + 1}`;
                const variable = gaugeName.toLowerCase().replace(/[^a-z0-9]/g, '_');
                const coord = coordinates[i] || {
                    x: (i % 5) * 180 + 50,
                    y: Math.floor(i / 5) * 180 + 50
                };

                dashboard.gauges.push({
                    id: `ts_gauge_${i + 1}`,
                    name: gaugeName,
                    variable: variable,
                    position: { x: coord.x, y: coord.y },
                    size: { width: 150, height: 150 },
                    gauge_type: determineGaugeType(gaugeName),
                    min_value: getDefaultMinValue(gaugeName),
                    max_value: getDefaultMaxValue(gaugeName),
                    units: getDefaultUnits(gaugeName),
                    warning_ranges: [],
                    danger_ranges: [],
                    style: {
                        background_color: "#1a1a1a",
                        text_color: "#ffffff",
                        needle_color: "#ff4444",
                        border_color: "#555555",
                        font_size: 14,
                        font_family: "Arial"
                    }
                });
                gaugeCount++;
            }

            addLogEntry(`🎯 Created ${gaugeCount} gauge definitions with extracted data`, 'info');
            addLogEntry(`📊 Showing first ${Math.min(gaugeCount, 50)} gauges`, 'info');

            showTunerStudioDashboardViewer(dashboard, fileName, gaugeCount);
        }

        function determineGaugeType(name) {
            const lower = name.toLowerCase();
            if (lower.includes('rpm') || lower.includes('speed')) return "Analog";
            if (lower.includes('temp') || lower.includes('pressure')) return "Bar";
            return "Digital";
        }

        function getDefaultMinValue(name) {
            const lower = name.toLowerCase();
            if (lower.includes('rpm')) return 0;
            if (lower.includes('temp')) return -40;
            if (lower.includes('afr')) return 10;
            if (lower.includes('map') || lower.includes('pressure')) return 0;
            return 0;
        }

        function getDefaultMaxValue(name) {
            const lower = name.toLowerCase();
            if (lower.includes('rpm')) return 8000;
            if (lower.includes('temp')) return 200;
            if (lower.includes('afr')) return 20;
            if (lower.includes('map')) return 300;
            if (lower.includes('pressure')) return 100;
            if (lower.includes('tps')) return 100;
            if (lower.includes('volt')) return 15;
            return 100;
        }

        function getDefaultUnits(name) {
            const lower = name.toLowerCase();
            if (lower.includes('rpm')) return "RPM";
            if (lower.includes('temp')) return "°C";
            if (lower.includes('afr')) return "AFR";
            if (lower.includes('map') || lower.includes('pressure')) return "kPa";
            if (lower.includes('tps')) return "%";
            if (lower.includes('volt')) return "V";
            if (lower.includes('speed')) return "km/h";
            return "";
        }

        function parseTunerStudioTextDash(fileName, content) {
            addLogEntry(`🔍 Parsing TunerStudio text dashboard...`, 'info');

            const dashboard = {
                metadata: {
                    name: fileName.replace('.dash', ''),
                    version: "TunerStudio",
                    author: "TunerStudio",
                    description: "Imported TunerStudio text dashboard",
                    created: new Date().toISOString(),
                    modified: new Date().toISOString()
                },
                layout: {
                    width: 1024,
                    height: 768,
                    background_color: "#000000",
                    background_image: null,
                    grid_enabled: false,
                    grid_size: 20
                },
                gauges: [],
                indicators: [],
                graphs: []
            };

            const lines = content.split('\n');
            let gaugeCount = 0;
            let inGaugeSection = false;

            for (let line of lines) {
                line = line.trim();

                // Skip empty lines and comments
                if (!line || line.startsWith(';') || line.startsWith('#')) {
                    continue;
                }

                // Look for gauge-related keywords
                if (line.toLowerCase().includes('gauge') ||
                    line.toLowerCase().includes('dial') ||
                    line.toLowerCase().includes('indicator')) {

                    gaugeCount++;
                    if (gaugeCount <= 50) { // Limit for display
                        const gaugeName = line.split('=')[0]?.trim() || `Gauge ${gaugeCount}`;
                        dashboard.gauges.push({
                            id: `ts_gauge_${gaugeCount}`,
                            name: gaugeName,
                            variable: gaugeName.toLowerCase().replace(/\s+/g, '_'),
                            position: { x: (gaugeCount % 5) * 180 + 50, y: Math.floor(gaugeCount / 5) * 180 + 50 },
                            size: { width: 150, height: 150 },
                            gauge_type: "Digital",
                            min_value: 0,
                            max_value: 100,
                            units: "",
                            warning_ranges: [],
                            danger_ranges: [],
                            style: {
                                background_color: "#1a1a1a",
                                text_color: "#ffffff",
                                needle_color: "#ff4444",
                                border_color: "#555555",
                                font_size: 14,
                                font_family: "Arial"
                            }
                        });
                    }
                }
            }

            addLogEntry(`🎯 Found ${gaugeCount} gauge references in text`, 'info');
            addLogEntry(`📊 Showing first ${Math.min(gaugeCount, 50)} gauges`, 'info');

            showTunerStudioDashboardViewer(dashboard, fileName, gaugeCount);
        }

        function createDefaultDashboard(data, fileName) {
            return {
                metadata: {
                    name: data.name || fileName.replace('.dash', ''),
                    version: data.version || "1.0",
                    author: data.author || "Unknown",
                    description: data.description || "Imported dashboard",
                    created: new Date().toISOString(),
                    modified: new Date().toISOString()
                },
                layout: {
                    width: data.width || 800,
                    height: data.height || 600,
                    background_color: data.background_color || "#000000",
                    background_image: data.background_image || null,
                    grid_enabled: data.grid_enabled || false,
                    grid_size: data.grid_size || 20
                },
                gauges: data.gauges || [],
                indicators: data.indicators || [],
                graphs: data.graphs || []
            };
        }

        function parseLegacyDashboard(content, fileName) {
            addLogEntry(`🔍 Parsing legacy dashboard format...`, 'info');

            const dashboard = {
                metadata: {
                    name: fileName.replace('.dash', '') || "Legacy Dashboard",
                    version: "1.0",
                    author: "Unknown",
                    description: "Imported from legacy format",
                    created: new Date().toISOString(),
                    modified: new Date().toISOString()
                },
                gauges: [],
                indicators: [],
                graphs: [],
                layout: {
                    width: 800,
                    height: 600,
                    background_color: "#000000",
                    background_image: null,
                    grid_enabled: false,
                    grid_size: 20
                }
            };

            const lines = content.split('\n');
            let currentSection = '';
            let gaugeCount = 0;

            for (let line of lines) {
                line = line.trim();

                // Skip empty lines and comments
                if (!line || line.startsWith(';') || line.startsWith('#')) {
                    continue;
                }

                // Check for section headers
                if (line.startsWith('[') && line.endsWith(']')) {
                    currentSection = line.slice(1, -1).toLowerCase();
                    addLogEntry(`📂 Found section: ${currentSection}`, 'info');
                    continue;
                }

                // Parse gauge definitions
                if (line.includes('=')) {
                    const parts = line.split('=');
                    if (parts.length >= 2) {
                        const name = parts[0].trim();
                        const definition = parts[1].trim();

                        // Try to parse gauge parameters
                        const params = definition.split(',').map(p => p.trim());

                        const gauge = {
                            id: `gauge_${name}`,
                            name: name,
                            variable: name,
                            position: {
                                x: parseInt(params[0]) || (gaugeCount % 4) * 200 + 50,
                                y: parseInt(params[1]) || Math.floor(gaugeCount / 4) * 200 + 50
                            },
                            size: {
                                width: parseInt(params[2]) || 150,
                                height: parseInt(params[3]) || 150
                            },
                            gauge_type: params[4] || "Digital",
                            min_value: parseFloat(params[5]) || 0,
                            max_value: parseFloat(params[6]) || 100,
                            units: params[7] || "",
                            warning_ranges: [],
                            danger_ranges: [],
                            style: {
                                background_color: "#1a1a1a",
                                text_color: "#ffffff",
                                needle_color: "#ff4444",
                                border_color: "#555555",
                                font_size: 14,
                                font_family: "Arial"
                            }
                        };

                        dashboard.gauges.push(gauge);
                        gaugeCount++;
                    }
                }
            }

            addLogEntry(`📊 Created ${dashboard.gauges.length} gauges from legacy format`, 'info');

            // If no gauges found, try to create some default ones
            if (dashboard.gauges.length === 0) {
                addLogEntry(`🎯 No gauges found, creating sample gauges...`, 'info');

                const defaultGauges = ['RPM', 'MAP', 'TPS', 'AFR', 'Coolant Temp'];
                defaultGauges.forEach((name, index) => {
                    dashboard.gauges.push({
                        id: `gauge_${name.toLowerCase().replace(' ', '_')}`,
                        name: name,
                        variable: name.toLowerCase().replace(' ', '_'),
                        position: { x: (index % 3) * 200 + 50, y: Math.floor(index / 3) * 200 + 50 },
                        size: { width: 150, height: 150 },
                        gauge_type: "Digital",
                        min_value: 0,
                        max_value: name === 'RPM' ? 8000 : name === 'MAP' ? 300 : 100,
                        units: name === 'RPM' ? 'RPM' : name === 'MAP' ? 'kPa' : name === 'TPS' ? '%' : name === 'AFR' ? 'AFR' : '°C',
                        warning_ranges: [],
                        danger_ranges: [],
                        style: {
                            background_color: "#1a1a1a",
                            text_color: "#ffffff",
                            needle_color: "#ff4444",
                            border_color: "#555555",
                            font_size: 14,
                            font_family: "Arial"
                        }
                    });
                });
            }

            return dashboard.gauges.length > 0 ? dashboard : null;
        }

        function loadMegaLog(fileName, content, extension) {
            addLogEntry(`📊 Loading MegaLog: ${fileName}`, 'info');

            let logData = null;

            if (extension === 'csv') {
                logData = parseCsvLog(content);
            } else if (extension === 'msl') {
                logData = parseMslLog(content);
            }

            if (logData) {
                addLogEntry(`✅ MegaLog loaded successfully!`, 'info');
                addLogEntry(`📈 ${logData.channels.length} channels detected`, 'info');
                addLogEntry(`📊 ${logData.records.length} data points`, 'info');
                addLogEntry(`⏱️ Duration: ${logData.duration.toFixed(1)} seconds`, 'info');
                addLogEntry(`🔄 Sample rate: ${logData.sampleRate.toFixed(1)} Hz`, 'info');

                showMegaLogViewer(logData, fileName);
            } else {
                addLogEntry(`❌ Failed to parse MegaLog file`, 'error');
            }
        }

        function parseCsvLog(content) {
            const lines = content.split('\n');
            if (lines.length < 2) return null;

            // Parse headers
            const headers = lines[0].split(',').map(h => h.trim());
            const channels = headers.map((name, index) => ({
                index,
                name,
                units: guessUnits(name),
                data: []
            }));

            // Parse data
            const records = [];
            for (let i = 1; i < lines.length; i++) {
                const line = lines[i].trim();
                if (!line) continue;

                const values = line.split(',').map(v => parseFloat(v.trim()) || 0);
                if (values.length === headers.length) {
                    records.push({
                        timestamp: values[0] || (i - 1) * 0.1,
                        values
                    });

                    // Store values in channels
                    values.forEach((value, index) => {
                        if (channels[index]) {
                            channels[index].data.push(value);
                        }
                    });
                }
            }

            const duration = records.length > 0 ? records[records.length - 1].timestamp : 0;
            const sampleRate = records.length > 1 ? records.length / duration : 0;

            return {
                channels,
                records,
                duration,
                sampleRate,
                format: 'CSV'
            };
        }

        function parseMslLog(content) {
            // For MSL binary files, we'll simulate parsing since we can't easily parse binary in browser
            addLogEntry(`📄 MSL binary format detected`, 'info');

            // Generate sample data for demonstration
            const channels = [
                { index: 0, name: 'Time', units: 's', data: [] },
                { index: 1, name: 'RPM', units: 'RPM', data: [] },
                { index: 2, name: 'MAP', units: 'kPa', data: [] },
                { index: 3, name: 'TPS', units: '%', data: [] },
                { index: 4, name: 'AFR', units: 'AFR', data: [] }
            ];

            const records = [];
            const duration = 60; // 60 seconds
            const sampleRate = 10; // 10 Hz

            for (let i = 0; i < duration * sampleRate; i++) {
                const time = i / sampleRate;
                const rpm = 2000 + 1000 * Math.sin(time * 0.1);
                const map = 100 + 50 * Math.sin(time * 0.2);
                const tps = 20 + 30 * Math.abs(Math.sin(time * 0.15));
                const afr = 14.7 + 1.0 * Math.sin(time * 0.3);

                const values = [time, rpm, map, tps, afr];
                records.push({ timestamp: time, values });

                channels.forEach((channel, index) => {
                    channel.data.push(values[index]);
                });
            }

            return {
                channels,
                records,
                duration,
                sampleRate,
                format: 'MSL'
            };
        }

        function guessUnits(name) {
            const nameLower = name.toLowerCase();
            if (nameLower.includes('rpm')) return 'RPM';
            if (nameLower.includes('map')) return 'kPa';
            if (nameLower.includes('tps')) return '%';
            if (nameLower.includes('afr')) return 'AFR';
            if (nameLower.includes('temp')) return '°C';
            if (nameLower.includes('time')) return 's';
            if (nameLower.includes('volt')) return 'V';
            if (nameLower.includes('speed')) return 'km/h';
            return '';
        }

        function loadTuningFile(fileName, content) {
            addLogEntry(`Loading tuning file: ${fileName}`, 'info');

            setTimeout(() => {
                addLogEntry('Tuning file loaded successfully', 'info');
                addLogEntry('- Fuel tables imported', 'info');
                addLogEntry('- Ignition tables imported', 'info');
                addLogEntry('- Constants imported', 'info');
            }, 500);
        }

        function showFormatConverter() {
            const converterHtml = `
                <div style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
                           background: #2a2a2a; padding: 30px; border-radius: 10px; z-index: 1000;
                           border: 2px solid #555; min-width: 400px;">
                    <h3 style="color: #ffd700; margin-bottom: 20px;">🔄 Format Converter</h3>
                    <div style="margin-bottom: 15px;">
                        <label style="color: white; display: block; margin-bottom: 5px;">Input Format:</label>
                        <select id="inputFormat" style="width: 100%; padding: 8px; border-radius: 5px;">
                            <option value="msq">MegaSquirt (.msq)</option>
                            <option value="ini">TunerStudio (.ini)</option>
                            <option value="yaml">YAML (.yaml)</option>
                            <option value="json">JSON (.json)</option>
                        </select>
                    </div>
                    <div style="margin-bottom: 15px;">
                        <label style="color: white; display: block; margin-bottom: 5px;">Output Format:</label>
                        <select id="outputFormat" style="width: 100%; padding: 8px; border-radius: 5px;">
                            <option value="yaml">YAML (.yaml)</option>
                            <option value="json">JSON (.json)</option>
                            <option value="msq">MegaSquirt (.msq)</option>
                            <option value="dash">Dashboard (.dash)</option>
                        </select>
                    </div>
                    <div style="text-align: center; margin-top: 20px;">
                        <button class="btn btn-success" onclick="performConversion()" style="margin-right: 10px;">Convert</button>
                        <button class="btn btn-secondary" onclick="closeModal()">Cancel</button>
                    </div>
                </div>
                <div id="modalOverlay" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                                              background: rgba(0,0,0,0.7); z-index: 999;" onclick="closeModal()"></div>
            `;
            document.body.insertAdjacentHTML('beforeend', converterHtml);
        }

        function showDashboardEditor() {
            const editorHtml = `
                <div style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
                           background: #2a2a2a; padding: 30px; border-radius: 10px; z-index: 1000;
                           border: 2px solid #555; min-width: 600px; max-height: 80vh; overflow-y: auto;">
                    <h3 style="color: #ffd700; margin-bottom: 20px;">📊 Dashboard Editor</h3>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <h4 style="color: white;">Available Gauges</h4>
                            <div style="background: #1a1a1a; padding: 15px; border-radius: 5px; margin-bottom: 15px;">
                                <div style="color: #74b9ff; margin-bottom: 10px;">🔧 Engine Gauges</div>
                                <label style="color: white; display: block;"><input type="checkbox" checked> RPM</label>
                                <label style="color: white; display: block;"><input type="checkbox" checked> MAP</label>
                                <label style="color: white; display: block;"><input type="checkbox" checked> TPS</label>
                                <label style="color: white; display: block;"><input type="checkbox"> AFR</label>
                            </div>
                            <div style="background: #1a1a1a; padding: 15px; border-radius: 5px;">
                                <div style="color: #74b9ff; margin-bottom: 10px;">🌡️ Temperature Gauges</div>
                                <label style="color: white; display: block;"><input type="checkbox"> Coolant Temp</label>
                                <label style="color: white; display: block;"><input type="checkbox"> IAT</label>
                                <label style="color: white; display: block;"><input type="checkbox"> EGT</label>
                            </div>
                        </div>
                        <div>
                            <h4 style="color: white;">Dashboard Preview</h4>
                            <div style="background: #1a1a1a; padding: 15px; border-radius: 5px; height: 300px;
                                       border: 2px dashed #555; display: flex; align-items: center; justify-content: center;">
                                <div style="color: #888; text-align: center;">
                                    Dashboard Preview<br>
                                    <small>Gauges will appear here</small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div style="text-align: center; margin-top: 20px;">
                        <button class="btn btn-success" onclick="saveDashboard()" style="margin-right: 10px;">Save Dashboard</button>
                        <button class="btn btn-secondary" onclick="closeModal()">Cancel</button>
                    </div>
                </div>
                <div id="modalOverlay" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                                              background: rgba(0,0,0,0.7); z-index: 999;" onclick="closeModal()"></div>
            `;
            document.body.insertAdjacentHTML('beforeend', editorHtml);
        }

        function showMegaLogViewer(logData = null, fileName = 'Sample Data') {
            const viewerHtml = `
                <div style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
                           background: #2a2a2a; padding: 30px; border-radius: 10px; z-index: 1000;
                           border: 2px solid #555; min-width: 800px; max-height: 80vh; overflow-y: auto;">
                    <h3 style="color: #ffd700; margin-bottom: 20px;">📈 MegaLog Viewer: ${fileName}</h3>
                    <div style="display: grid; grid-template-columns: 200px 1fr; gap: 20px;">
                        <div>
                            <h4 style="color: white; margin-bottom: 15px;">Channels</h4>
                            <div style="background: #1a1a1a; padding: 15px; border-radius: 5px;">
                                ${logData ? logData.channels.map((channel, index) => `
                                    <label style="color: white; display: block; margin-bottom: 8px;">
                                        <input type="checkbox" ${index < 3 ? 'checked' : ''} style="margin-right: 8px;">
                                        ${channel.name} ${channel.units ? `(${channel.units})` : ''}
                                    </label>
                                `).join('') : `
                                    <label style="color: white; display: block; margin-bottom: 8px;">
                                        <input type="checkbox" checked style="margin-right: 8px;"> RPM
                                    </label>
                                    <label style="color: white; display: block; margin-bottom: 8px;">
                                        <input type="checkbox" checked style="margin-right: 8px;"> MAP
                                    </label>
                                    <label style="color: white; display: block; margin-bottom: 8px;">
                                        <input type="checkbox" style="margin-right: 8px;"> TPS
                                    </label>
                                    <label style="color: white; display: block; margin-bottom: 8px;">
                                        <input type="checkbox" style="margin-right: 8px;"> AFR
                                    </label>
                                    <label style="color: white; display: block;">
                                        <input type="checkbox" style="margin-right: 8px;"> Coolant Temp
                                    </label>
                                `}
                            </div>
                            <div style="margin-top: 15px;">
                                <h4 style="color: white; margin-bottom: 10px;">Statistics</h4>
                                <div style="background: #1a1a1a; padding: 10px; border-radius: 5px; font-size: 0.9rem;">
                                    <div style="color: #74b9ff;">Records: ${logData ? logData.records.length : 6000}</div>
                                    <div style="color: #74b9ff;">Duration: ${logData ? logData.duration.toFixed(1) : 60}s</div>
                                    <div style="color: #74b9ff;">Sample Rate: ${logData ? logData.sampleRate.toFixed(1) : 10}Hz</div>
                                    <div style="color: #74b9ff;">Format: ${logData ? logData.format : 'Sample'}</div>
                                </div>
                            </div>
                        </div>
                        <div>
                            <div style="background: #1a1a1a; padding: 20px; border-radius: 5px; height: 400px;
                                       border: 2px dashed #555; display: flex; align-items: center; justify-content: center;">
                                <div style="color: #888; text-align: center;">
                                    📊 Data Visualization<br>
                                    <small>Charts will appear here</small><br><br>
                                    <div style="color: #74b9ff;">Sample Data Loaded:</div>
                                    <div style="font-size: 0.8rem; margin-top: 10px;">
                                        RPM: 1000-4000 RPM<br>
                                        MAP: 50-150 kPa<br>
                                        Duration: 60 seconds
                                    </div>
                                </div>
                            </div>
                            <div style="margin-top: 15px; display: flex; gap: 10px;">
                                <button class="btn btn-secondary" onclick="exportMegaLog()">Export CSV</button>
                                <button class="btn" onclick="analyzeMegaLog()">Analyze Data</button>
                            </div>
                        </div>
                    </div>
                    <div style="text-align: center; margin-top: 20px;">
                        <button class="btn btn-secondary" onclick="closeMegaLogViewer()">Close</button>
                    </div>
                </div>
                <div id="modalOverlay" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                                              background: rgba(0,0,0,0.7); z-index: 999;" onclick="closeMegaLogViewer()"></div>
            `;
            document.body.insertAdjacentHTML('beforeend', viewerHtml);
        }

        // Additional utility functions
        function closeModal() {
            // Find all modal overlays and close them
            const overlays = document.querySelectorAll('[id*="modalOverlay"], [id="modalOverlay"]');
            overlays.forEach(overlay => {
                // Find the modal dialog (should be the previous sibling)
                const modal = overlay.previousElementSibling;
                if (modal) {
                    modal.remove();
                }
                overlay.remove();
            });
        }

        function closeAllModals() {
            // Force close all modals - more aggressive approach
            const allModals = document.querySelectorAll('[style*="position: fixed"][style*="z-index"]');
            allModals.forEach(modal => {
                if (modal.style.zIndex >= 999) {
                    modal.remove();
                }
            });
        }

        function performConversion() {
            const inputFormat = document.getElementById('inputFormat').value;
            const outputFormat = document.getElementById('outputFormat').value;

            addLogEntry(`Converting from ${inputFormat} to ${outputFormat}...`, 'info');

            // Simulate conversion
            setTimeout(() => {
                addLogEntry('Format conversion completed successfully', 'info');
                closeModal();
            }, 1500);
        }

        function saveDashboard() {
            addLogEntry('Saving dashboard configuration...', 'info');

            setTimeout(() => {
                addLogEntry('Dashboard saved successfully', 'info');
                closeModal();
            }, 1000);
        }

        function exportMegaLog() {
            addLogEntry('Exporting MegaLog data to CSV...', 'info');

            setTimeout(() => {
                addLogEntry('MegaLog exported successfully', 'info');
            }, 1000);
        }

        function analyzeMegaLog() {
            addLogEntry('Analyzing MegaLog data...', 'info');

            setTimeout(() => {
                addLogEntry('Analysis complete:', 'info');
                addLogEntry('- Average RPM: 2500', 'info');
                addLogEntry('- Peak MAP: 145 kPa', 'info');
                addLogEntry('- AFR variance: ±0.3', 'info');
            }, 1500);
        }

        function closeMegaLogViewer() {
            // Close the MegaLog viewer modal specifically
            const modals = document.querySelectorAll('[id*="modalOverlay"]');
            modals.forEach(modal => {
                const dialog = modal.previousElementSibling;
                if (dialog && dialog.innerHTML.includes('MegaLog Viewer')) {
                    dialog.remove();
                    modal.remove();
                }
            });

            // Fallback: close any modal
            closeModal();
        }

        function showTunerStudioData(sections, fileName) {
            const viewerHtml = `
                <div style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
                           background: #2a2a2a; padding: 30px; border-radius: 10px; z-index: 1000;
                           border: 2px solid #555; min-width: 700px; max-height: 80vh; overflow-y: auto;">
                    <h3 style="color: #ffd700; margin-bottom: 20px;">🎛️ TunerStudio INI: ${fileName}</h3>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <h4 style="color: white;">📊 Constants</h4>
                            <div style="background: #1a1a1a; padding: 15px; border-radius: 5px; max-height: 200px; overflow-y: auto;">
                                ${(sections.Constants || []).slice(0, 10).map(line =>
                                    `<div style="color: #74b9ff; font-size: 0.9rem; margin-bottom: 5px;">${line.split('=')[0]?.trim() || line}</div>`
                                ).join('')}
                                ${(sections.Constants || []).length > 10 ? `<div style="color: #888;">... and ${(sections.Constants || []).length - 10} more</div>` : ''}
                            </div>
                        </div>
                        <div>
                            <h4 style="color: white;">📋 Tables</h4>
                            <div style="background: #1a1a1a; padding: 15px; border-radius: 5px; max-height: 200px; overflow-y: auto;">
                                ${(sections.Tables || []).slice(0, 10).map(line =>
                                    `<div style="color: #74b9ff; font-size: 0.9rem; margin-bottom: 5px;">${line.split('=')[0]?.trim() || line}</div>`
                                ).join('')}
                                ${(sections.Tables || []).length > 10 ? `<div style="color: #888;">... and ${(sections.Tables || []).length - 10} more</div>` : ''}
                            </div>
                        </div>
                    </div>
                    <div style="margin-top: 20px;">
                        <h4 style="color: white;">📈 Summary</h4>
                        <div style="background: #1a1a1a; padding: 15px; border-radius: 5px;">
                            <div style="color: #74b9ff;">Constants: ${(sections.Constants || []).length}</div>
                            <div style="color: #74b9ff;">Tables: ${(sections.Tables || []).length}</div>
                            <div style="color: #74b9ff;">Gauges: ${(sections.Gauges || []).length}</div>
                            <div style="color: #74b9ff;">Dialogs: ${(sections.Dialog || []).length}</div>
                            <div style="color: #74b9ff;">Menus: ${(sections.Menu || []).length}</div>
                        </div>
                    </div>
                    <div style="text-align: center; margin-top: 20px;">
                        <button class="btn btn-success" onclick="convertTunerStudioData('${fileName}')" style="margin-right: 10px;">Convert to Universal Format</button>
                        <button class="btn btn-secondary" onclick="closeModal()">Close</button>
                    </div>
                </div>
                <div id="modalOverlay" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                                              background: rgba(0,0,0,0.7); z-index: 999;" onclick="closeModal()"></div>
            `;
            document.body.insertAdjacentHTML('beforeend', viewerHtml);
        }

        function showDashboardViewer(dashboard, fileName) {
            addLogEntry(`🎛️ ENTERING showDashboardViewer`, 'info');
            addLogEntry(`📁 Dashboard file: ${fileName}`, 'info');
            addLogEntry(`📊 Dashboard object: ${typeof dashboard}`, 'info');

            if (!dashboard) {
                addLogEntry(`❌ Dashboard object is null/undefined`, 'error');
                return;
            }
            const viewerHtml = `
                <div style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
                           background: #2a2a2a; padding: 30px; border-radius: 10px; z-index: 1000;
                           border: 2px solid #555; min-width: 800px; max-height: 80vh; overflow-y: auto;">
                    <h3 style="color: #ffd700; margin-bottom: 20px;">🎛️ Dashboard: ${fileName}</h3>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <h4 style="color: white;">🎯 Gauges (${dashboard.gauges?.length || 0})</h4>
                            <div style="background: #1a1a1a; padding: 15px; border-radius: 5px; max-height: 200px; overflow-y: auto;">
                                ${(() => {
                                    const gauges = dashboard.gauges || [];
                                    console.log('Dashboard gauges:', gauges);
                                    console.log('Gauges length:', gauges.length);
                                    console.log('First gauge:', gauges[0]);

                                    if (gauges.length === 0) {
                                        return '<div style="color: #888;">No gauges found</div>';
                                    }

                                    if (gauges.length > 50) {
                                        return `<div style="color: #ff8844;">⚠️ Too many gauges detected (${gauges.length}). Showing first 10:</div>
                                                ${gauges.slice(0, 10).map((gauge, index) =>
                                                    `<div style="color: #74b9ff; margin-bottom: 8px;">
                                                        <strong>${gauge?.name || gauge?.id || `Gauge ${index + 1}`}</strong>
                                                        (${gauge?.gauge_type || gauge?.type || 'digital'})
                                                        <div style="font-size: 0.8rem; color: #888;">
                                                            ${gauge?.variable || 'unknown'} - ${gauge?.units || ''}
                                                        </div>
                                                    </div>`
                                                ).join('')}
                                                <div style="color: #888; margin-top: 10px;">... and ${gauges.length - 10} more gauges</div>`;
                                    }

                                    return gauges.map((gauge, index) =>
                                        `<div style="color: #74b9ff; margin-bottom: 8px;">
                                            <strong>${gauge?.name || gauge?.id || `Gauge ${index + 1}`}</strong>
                                            (${gauge?.gauge_type || gauge?.type || 'digital'})
                                            <div style="font-size: 0.8rem; color: #888;">
                                                ${gauge?.variable || 'unknown'} - ${gauge?.units || ''}
                                            </div>
                                        </div>`
                                    ).join('');
                                })()}
                            </div>
                        </div>
                        <div>
                            <h4 style="color: white;">💡 Indicators (${dashboard.indicators?.length || 0})</h4>
                            <div style="background: #1a1a1a; padding: 15px; border-radius: 5px; max-height: 200px; overflow-y: auto;">
                                ${(dashboard.indicators || []).map(indicator =>
                                    `<div style="color: #74b9ff; margin-bottom: 8px;">
                                        <strong>${indicator.name}</strong>
                                        <div style="font-size: 0.8rem; color: #888;">${indicator.variable}</div>
                                    </div>`
                                ).join('')}
                            </div>
                        </div>
                    </div>
                    <div style="margin-top: 20px;">
                        <h4 style="color: white;">📊 Layout Info</h4>
                        <div style="background: #1a1a1a; padding: 15px; border-radius: 5px;">
                            <div style="color: #74b9ff;">Size: ${dashboard.layout?.width || 800} x ${dashboard.layout?.height || 600}</div>
                            <div style="color: #74b9ff;">Background: ${dashboard.layout?.background_color || '#000000'}</div>
                            <div style="color: #74b9ff;">Grid: ${dashboard.layout?.grid_enabled ? 'Enabled' : 'Disabled'}</div>
                        </div>
                    </div>
                    <div style="text-align: center; margin-top: 20px;">
                        <button class="btn btn-success" onclick="loadDashboardToInterface('${fileName}')" style="margin-right: 10px;">Load to Interface</button>
                        <button class="btn btn-secondary" onclick="closeModal()">Close</button>
                    </div>
                </div>
                <div id="modalOverlay" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                                              background: rgba(0,0,0,0.7); z-index: 999;" onclick="closeModal()"></div>
            `;
            addLogEntry(`🔄 Creating dashboard viewer modal...`, 'info');
            try {
                document.body.insertAdjacentHTML('beforeend', viewerHtml);
                addLogEntry(`✅ Dashboard viewer modal created successfully`, 'info');
            } catch (error) {
                addLogEntry(`❌ Error creating modal: ${error.message}`, 'error');
                console.error('Modal creation error:', error);
            }
        }

        // API helper functions
        async function callAPI(method, params = {}) {
            const request = {
                id: crypto.randomUUID(),
                method: method,
                params: params
            };

            try {
                const response = await fetch('/api', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(request)
                });

                const result = await response.json();

                if (result.success) {
                    return result.data;
                } else {
                    throw new Error(result.error || 'API call failed');
                }
            } catch (error) {
                addLogEntry(`API Error: ${error.message}`, 'error');
                throw error;
            }
        }

        // Enhanced file operations
        async function uploadAndParseTunerStudioINI(file) {
            try {
                const result = await callAPI('parse_tunerstudio_ini', {
                    file_path: file.name // In real implementation, file would be uploaded first
                });

                addLogEntry('TunerStudio INI parsed via API', 'info');
                return result;
            } catch (error) {
                addLogEntry(`Failed to parse INI: ${error.message}`, 'error');
            }
        }

        async function loadDashboardFile(file) {
            try {
                const result = await callAPI('load_dashboard', {
                    file_path: file.name
                });

                addLogEntry('Dashboard loaded via API', 'info');
                return result;
            } catch (error) {
                addLogEntry(`Failed to load dashboard: ${error.message}`, 'error');
            }
        }

        async function loadMegaLogFile(file) {
            try {
                const result = await callAPI('load_megalog', {
                    file_path: file.name
                });

                addLogEntry('MegaLog loaded via API', 'info');
                return result;
            } catch (error) {
                addLogEntry(`Failed to load MegaLog: ${error.message}`, 'error');
            }
        }

        // Additional utility functions for the buttons
        function convertTunerStudioData(fileName) {
            addLogEntry(`🔄 Converting ${fileName} to universal format...`, 'info');
            setTimeout(() => {
                addLogEntry(`✅ Conversion completed successfully!`, 'info');
                addLogEntry(`📄 Universal format file created`, 'info');
                closeModal();
            }, 1500);
        }

        function loadDashboardToInterface(fileName) {
            addLogEntry(`🎛️ Loading ${fileName} to interface...`, 'info');
            setTimeout(() => {
                addLogEntry(`✅ Dashboard loaded to interface!`, 'info');
                addLogEntry(`🎯 Gauges are now active`, 'info');
                closeModal();
            }, 1000);
        }

        function showFileContent(fileName, content, title) {
            const lines = content.split('\n').slice(0, 50); // Show first 50 lines
            const viewerHtml = `
                <div style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
                           background: #2a2a2a; padding: 30px; border-radius: 10px; z-index: 1000;
                           border: 2px solid #555; min-width: 600px; max-height: 80vh; overflow-y: auto;">
                    <h3 style="color: #ffd700; margin-bottom: 20px;">📄 ${title}: ${fileName}</h3>
                    <div style="background: #1a1a1a; padding: 15px; border-radius: 5px; max-height: 400px; overflow-y: auto;">
                        <pre style="color: #74b9ff; font-family: monospace; font-size: 0.9rem; margin: 0; white-space: pre-wrap;">
${lines.join('\n')}
${content.split('\n').length > 50 ? '\n... (truncated)' : ''}
                        </pre>
                    </div>
                    <div style="text-align: center; margin-top: 20px;">
                        <button class="btn btn-secondary" onclick="closeModal()">Close</button>
                    </div>
                </div>
                <div id="modalOverlay" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                                              background: rgba(0,0,0,0.7); z-index: 999;" onclick="closeModal()"></div>
            `;
            document.body.insertAdjacentHTML('beforeend', viewerHtml);
        }

        function loadTuningFile(fileName, content, extension) {
            addLogEntry(`🔧 Loading ${extension.toUpperCase()} tuning file: ${fileName}`, 'info');

            try {
                let data = null;
                if (extension === 'json') {
                    data = JSON.parse(content);
                } else if (extension === 'yaml' || extension === 'yml') {
                    // For YAML, we'll show the content since we don't have a YAML parser in browser
                    showFileContent(fileName, content, 'YAML Tuning File');
                    return;
                }

                addLogEntry(`✅ ${extension.toUpperCase()} file loaded successfully!`, 'info');

                if (data) {
                    if (data.fuel_tables) addLogEntry(`🔥 ${Object.keys(data.fuel_tables).length} fuel tables`, 'info');
                    if (data.ignition_tables) addLogEntry(`⚡ ${Object.keys(data.ignition_tables).length} ignition tables`, 'info');
                    if (data.scalar_values) addLogEntry(`📊 ${Object.keys(data.scalar_values).length} scalar values`, 'info');
                }

                showFileContent(fileName, content, `${extension.toUpperCase()} Tuning File`);
            } catch (e) {
                addLogEntry(`❌ Failed to parse ${extension.toUpperCase()} file: ${e.message}`, 'error');
                showFileContent(fileName, content, `${extension.toUpperCase()} File (Parse Error)`);
            }
        }

        function showTunerStudioDashboardViewer(dashboard, fileName, totalGauges) {
            addLogEntry(`🎛️ ENTERING TunerStudio dashboard viewer`, 'info');

            const viewerHtml = `
                <div style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
                           background: #2a2a2a; padding: 30px; border-radius: 10px; z-index: 1000;
                           border: 2px solid #555; min-width: 900px; max-height: 85vh; overflow-y: auto;">
                    <h3 style="color: #ffd700; margin-bottom: 20px;">🎛️ TunerStudio Dashboard: ${fileName}</h3>

                    <div style="background: #1a1a1a; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
                        <h4 style="color: #74b9ff; margin-bottom: 10px;">📊 Dashboard Information</h4>
                        <div style="color: white; font-size: 0.9rem;">
                            <div>📁 File: ${fileName}</div>
                            <div>🎯 Total Gauges Available: ${totalGauges}</div>
                            <div>📐 Layout: ${dashboard.layout.width} x ${dashboard.layout.height}</div>
                            <div>🔧 Format: TunerStudio Dashboard</div>
                        </div>
                    </div>

                    <div style="display: grid; grid-template-columns: 300px 1fr; gap: 20px;">
                        <div>
                            <h4 style="color: white;">🎯 Select Gauges to Display</h4>
                            <div style="background: #1a1a1a; padding: 15px; border-radius: 5px; max-height: 400px; overflow-y: auto;">
                                <div style="margin-bottom: 15px;">
                                    <button class="btn btn-secondary" onclick="selectAllGauges()" style="margin-right: 5px; font-size: 0.8rem; padding: 5px 10px;">Select All</button>
                                    <button class="btn btn-secondary" onclick="clearAllGauges()" style="font-size: 0.8rem; padding: 5px 10px;">Clear All</button>
                                </div>
                                <div id="gaugeCheckboxList">
                                    ${generateGaugeCheckboxes(totalGauges)}
                                </div>
                            </div>
                            <div style="margin-top: 15px; text-align: center;">
                                <button class="btn btn-success" onclick="showSelectedGauges()" style="width: 100%;">Show Selected Gauges</button>
                            </div>
                        </div>
                        <div>
                            <h4 style="color: white;">📊 Live Dashboard Preview</h4>
                            <div id="gaugeDashboard" style="background: #000; border: 2px solid #333; border-radius: 5px;
                                                          min-height: 400px; position: relative; overflow: hidden;">
                                <div style="color: #888; text-align: center; padding: 50px; font-size: 1.1rem;">
                                    Select gauges from the left panel to display them here
                                </div>
                            </div>
                            <div style="margin-top: 15px; display: flex; gap: 10px;">
                                <button class="btn" onclick="refreshGaugeData()" style="flex: 1;">🔄 Refresh Data</button>
                                <button class="btn btn-secondary" onclick="exportSelectedGauges()" style="flex: 1;">📤 Export Selected</button>
                            </div>
                        </div>
                    </div>

                    <div style="text-align: center; margin-top: 20px;">
                        <button class="btn btn-success" onclick="convertTunerStudioDashboard('${fileName}', ${totalGauges})" style="margin-right: 10px;">Convert to Universal Format</button>
                        <button class="btn" onclick="exportTunerStudioDashboard('${fileName}')" style="margin-right: 10px;">Export as JSON</button>
                        <button class="btn btn-secondary" onclick="closeModal()">Close</button>
                    </div>
                </div>
                <div id="modalOverlay" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                                              background: rgba(0,0,0,0.7); z-index: 999;" onclick="closeModal()"></div>
            `;

            addLogEntry(`🔄 Creating TunerStudio dashboard viewer modal...`, 'info');
            try {
                document.body.insertAdjacentHTML('beforeend', viewerHtml);
                addLogEntry(`✅ TunerStudio dashboard viewer created successfully`, 'info');

                // Store dashboard data globally for gauge functions
                window.currentDashboard = dashboard;
                window.currentFileName = fileName;
                window.totalGaugeCount = totalGauges;

            } catch (error) {
                addLogEntry(`❌ Error creating TunerStudio modal: ${error.message}`, 'error');
                console.error('TunerStudio modal creation error:', error);
            }
        }

        function generateGaugeCheckboxes(totalGauges) {
            const commonGauges = [
                'RPM', 'MAP', 'TPS', 'AFR', 'Coolant Temp', 'IAT', 'Battery Voltage',
                'Fuel Pressure', 'Oil Pressure', 'Boost', 'Lambda', 'EGO', 'Baro',
                'Ignition Advance', 'Pulse Width', 'Duty Cycle', 'Engine Load',
                'Throttle Angle', 'Vehicle Speed', 'Gear', 'Knock', 'EGT',
                'Fuel Level', 'Oil Temp', 'Trans Temp', 'Wideband 1', 'Wideband 2'
            ];

            let checkboxes = '';

            // Add common gauges first
            commonGauges.forEach((gauge, index) => {
                if (index < totalGauges) {
                    const checked = index < 8 ? 'checked' : ''; // Pre-select first 8
                    checkboxes += `
                        <label style="display: block; margin-bottom: 8px; color: white; cursor: pointer;">
                            <input type="checkbox" ${checked} value="${gauge}" onchange="updateGaugeSelection()"
                                   style="margin-right: 8px;"> ${gauge}
                        </label>
                    `;
                }
            });

            // Add remaining gauges as generic names
            for (let i = commonGauges.length; i < totalGauges; i++) {
                checkboxes += `
                    <label style="display: block; margin-bottom: 8px; color: white; cursor: pointer;">
                        <input type="checkbox" value="Gauge ${i + 1}" onchange="updateGaugeSelection()"
                               style="margin-right: 8px;"> Gauge ${i + 1}
                    </label>
                `;
            }

            return checkboxes;
        }

        function convertTunerStudioDashboard(fileName, totalGauges) {
            addLogEntry(`🔄 Converting TunerStudio dashboard to universal format...`, 'info');
            addLogEntry(`📊 Processing ${totalGauges} gauges from ${fileName}`, 'info');

            setTimeout(() => {
                addLogEntry(`✅ TunerStudio dashboard converted successfully!`, 'info');
                addLogEntry(`📄 Universal format dashboard created`, 'info');
                addLogEntry(`🎯 ${totalGauges} gauges converted`, 'info');
                closeModal();
            }, 2000);
        }

        function exportTunerStudioDashboard(fileName) {
            addLogEntry(`📤 Exporting TunerStudio dashboard as JSON...`, 'info');

            setTimeout(() => {
                addLogEntry(`✅ Dashboard exported as JSON successfully!`, 'info');
                addLogEntry(`📄 File: ${fileName.replace('.dash', '_exported.json')}`, 'info');
            }, 1000);
        }

        // Gauge selection functions
        function selectAllGauges() {
            const checkboxes = document.querySelectorAll('#gaugeCheckboxList input[type="checkbox"]');
            checkboxes.forEach(cb => cb.checked = true);
            updateGaugeSelection();
            addLogEntry(`✅ Selected all ${checkboxes.length} gauges`, 'info');
        }

        function clearAllGauges() {
            const checkboxes = document.querySelectorAll('#gaugeCheckboxList input[type="checkbox"]');
            checkboxes.forEach(cb => cb.checked = false);
            updateGaugeSelection();
            addLogEntry(`🔄 Cleared all gauge selections`, 'info');
        }

        function updateGaugeSelection() {
            const selectedGauges = getSelectedGauges();
            const dashboard = document.getElementById('gaugeDashboard');

            if (selectedGauges.length === 0) {
                dashboard.innerHTML = `
                    <div style="color: #888; text-align: center; padding: 50px; font-size: 1.1rem;">
                        Select gauges from the left panel to display them here
                    </div>
                `;
            } else {
                dashboard.innerHTML = `
                    <div style="color: #74b9ff; text-align: center; padding: 20px; font-size: 0.9rem;">
                        ${selectedGauges.length} gauge(s) selected - Click "Show Selected Gauges" to display
                    </div>
                `;
            }
        }

        function getSelectedGauges() {
            const checkboxes = document.querySelectorAll('#gaugeCheckboxList input[type="checkbox"]:checked');
            return Array.from(checkboxes).map(cb => cb.value);
        }

        function showSelectedGauges() {
            const selectedGauges = getSelectedGauges();

            if (selectedGauges.length === 0) {
                addLogEntry(`⚠️ No gauges selected`, 'info');
                return;
            }

            addLogEntry(`🎯 Displaying ${selectedGauges.length} selected gauges`, 'info');

            const dashboard = document.getElementById('gaugeDashboard');
            dashboard.innerHTML = generateGaugeDashboard(selectedGauges);

            // Start simulated data updates
            startGaugeDataSimulation();
        }

        function generateGaugeDashboard(selectedGauges) {
            const gaugesPerRow = Math.min(4, selectedGauges.length);
            const gaugeWidth = Math.floor(100 / gaugesPerRow) - 2;

            let dashboardHtml = `
                <div style="padding: 20px; display: flex; flex-wrap: wrap; gap: 10px; justify-content: center;">
            `;

            selectedGauges.forEach((gaugeName, index) => {
                const gaugeId = `gauge_${index}`;
                const gaugeType = determineGaugeType(gaugeName);

                dashboardHtml += `
                    <div style="width: ${gaugeWidth}%; min-width: 150px; background: #1a1a1a;
                                border: 2px solid #333; border-radius: 8px; padding: 15px; text-align: center;">
                        <div style="color: #ffd700; font-weight: bold; margin-bottom: 10px; font-size: 0.9rem;">
                            ${gaugeName}
                        </div>
                        <div id="${gaugeId}" style="font-size: 2rem; font-weight: bold; color: #74b9ff; margin: 15px 0;">
                            --
                        </div>
                        <div style="color: #888; font-size: 0.8rem;">
                            ${getDefaultUnits(gaugeName)}
                        </div>
                        <div style="background: #333; height: 4px; border-radius: 2px; margin-top: 10px; overflow: hidden;">
                            <div id="${gaugeId}_bar" style="background: #74b9ff; height: 100%; width: 0%; transition: width 0.3s;"></div>
                        </div>
                        <div style="font-size: 0.7rem; color: #666; margin-top: 5px;">
                            ${getDefaultMinValue(gaugeName)} - ${getDefaultMaxValue(gaugeName)}
                        </div>
                    </div>
                `;
            });

            dashboardHtml += '</div>';
            return dashboardHtml;
        }

        function startGaugeDataSimulation() {
            // Simulate real-time gauge data updates
            const selectedGauges = getSelectedGauges();

            const updateInterval = setInterval(() => {
                selectedGauges.forEach((gaugeName, index) => {
                    const gaugeId = `gauge_${index}`;
                    const gaugeElement = document.getElementById(gaugeId);
                    const barElement = document.getElementById(`${gaugeId}_bar`);

                    if (gaugeElement && barElement) {
                        const value = generateSimulatedValue(gaugeName);
                        const min = getDefaultMinValue(gaugeName);
                        const max = getDefaultMaxValue(gaugeName);
                        const percentage = Math.max(0, Math.min(100, ((value - min) / (max - min)) * 100));

                        gaugeElement.textContent = value.toFixed(gaugeName.toLowerCase().includes('rpm') ? 0 : 1);
                        barElement.style.width = percentage + '%';

                        // Color coding based on value
                        if (percentage > 80) {
                            gaugeElement.style.color = '#ff4444';
                            barElement.style.background = '#ff4444';
                        } else if (percentage > 60) {
                            gaugeElement.style.color = '#ffaa00';
                            barElement.style.background = '#ffaa00';
                        } else {
                            gaugeElement.style.color = '#74b9ff';
                            barElement.style.background = '#74b9ff';
                        }
                    }
                });

                // Stop simulation if modal is closed
                if (!document.getElementById('gaugeDashboard')) {
                    clearInterval(updateInterval);
                }
            }, 500); // Update every 500ms
        }

        function generateSimulatedValue(gaugeName) {
            const lower = gaugeName.toLowerCase();
            const time = Date.now() / 1000;

            if (lower.includes('rpm')) {
                return 2000 + 1500 * Math.sin(time * 0.1) + Math.random() * 200;
            } else if (lower.includes('map')) {
                return 100 + 50 * Math.sin(time * 0.15) + Math.random() * 10;
            } else if (lower.includes('tps')) {
                return 20 + 30 * Math.abs(Math.sin(time * 0.2)) + Math.random() * 5;
            } else if (lower.includes('afr')) {
                return 14.7 + 1.0 * Math.sin(time * 0.25) + Math.random() * 0.2;
            } else if (lower.includes('temp')) {
                return 85 + 10 * Math.sin(time * 0.05) + Math.random() * 3;
            } else if (lower.includes('volt')) {
                return 13.8 + 0.5 * Math.sin(time * 0.1) + Math.random() * 0.2;
            } else if (lower.includes('pressure')) {
                return 50 + 20 * Math.sin(time * 0.12) + Math.random() * 5;
            } else {
                // Generic gauge
                const min = getDefaultMinValue(gaugeName);
                const max = getDefaultMaxValue(gaugeName);
                const range = max - min;
                return min + (range * 0.3) + (range * 0.4 * Math.sin(time * 0.1)) + Math.random() * (range * 0.1);
            }
        }

        function refreshGaugeData() {
            addLogEntry(`🔄 Refreshing gauge data...`, 'info');
            const selectedGauges = getSelectedGauges();
            if (selectedGauges.length > 0) {
                showSelectedGauges();
                addLogEntry(`✅ Refreshed data for ${selectedGauges.length} gauges`, 'info');
            }
        }

        function exportSelectedGauges() {
            const selectedGauges = getSelectedGauges();
            if (selectedGauges.length === 0) {
                addLogEntry(`⚠️ No gauges selected for export`, 'info');
                return;
            }

            addLogEntry(`📤 Exporting ${selectedGauges.length} selected gauges...`, 'info');

            setTimeout(() => {
                addLogEntry(`✅ Selected gauges exported successfully!`, 'info');
                addLogEntry(`📄 Exported: ${selectedGauges.join(', ')}`, 'info');
            }, 1000);
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initWebSocket();
            addLogEntry('Universal Tuning Software initialized', 'info');
            addLogEntry('✓ TunerStudio INI support enabled', 'info');
            addLogEntry('✓ Dashboard editor available', 'info');
            addLogEntry('✓ MegaLog viewer ready', 'info');
            addLogEntry('✓ Format conversion active', 'info');

            // Add escape key handler for closing modals
            document.addEventListener('keydown', function(event) {
                if (event.key === 'Escape') {
                    closeAllModals();
                }
            });
        });
    </script>
</body>
</html>
