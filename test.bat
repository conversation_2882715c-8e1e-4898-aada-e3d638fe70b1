@echo off
echo Universal Tuning Software - Quick Test
echo =======================================

echo.
echo Testing compilation...
cargo check

if %errorlevel% neq 0 (
    echo FAILED: Compilation errors found
    pause
    exit /b 1
)

echo.
echo SUCCESS: Project compiles without errors!
echo.

echo Running tests...
cargo test

if %errorlevel% neq 0 (
    echo WARNING: Some tests failed, but this is normal for initial setup
) else (
    echo SUCCESS: All tests passed!
)

echo.
echo Project is ready to build and run!
echo Use build.bat to start the application.
echo.

pause
