[package]
name = "tuning-web"
version = "0.1.0"
edition = "2021"
description = "Web interface for the tuning software"
license = "MIT OR Apache-2.0"

[lib]
crate-type = ["cdylib", "rlib"]

[dependencies]
# Workspace dependencies
serde = { workspace = true }
serde_json = { workspace = true }
anyhow = { workspace = true }

# Local dependencies
tuning-core = { path = "../tuning-core" }
tuning-hal = { path = "../tuning-hal" }
tuning-formats = { path = "../tuning-formats" }

# Web framework
axum = { version = "0.7", features = ["ws"] }
tower = "0.4"
tower-http = { version = "0.5", features = ["cors", "fs"] }
hyper = "1.0"
futures-util = "0.3"

# WebAssembly
wasm-bindgen = { workspace = true, optional = true }
wasm-bindgen-futures = { workspace = true, optional = true }
js-sys = { workspace = true, optional = true }
web-sys = { workspace = true, optional = true, features = [
  "console",
  "Document",
  "Element",
  "HtmlElement",
  "Window",
  "WebSocket",
  "MessageEvent",
  "ErrorEvent",
  "CloseEvent",
  "Blob",
  "FileReader",
  "File",
  "FileList",
  "HtmlInputElement",
] }

# Async runtime
tokio = { workspace = true, features = ["macros", "rt-multi-thread"] }

# Utilities
uuid = { workspace = true }
chrono = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true }

[features]
default = ["server"]
server = []
wasm = ["wasm-bindgen", "js-sys", "web-sys"]
