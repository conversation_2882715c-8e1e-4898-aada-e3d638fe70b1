//! YAML format support

use crate::TuningData;
use anyhow::Result;
use std::path::Path;

/// YAML format handler
pub struct YamlFormat;

impl YamlFormat {
    /// Load YAML file
    pub fn load<P: AsRef<Path>>(path: P) -> Result<TuningData> {
        let content = std::fs::read_to_string(path)?;
        let mut data: TuningData = serde_yaml::from_str(&content)?;
        data.metadata.source_format = "yaml".to_string();
        Ok(data)
    }

    /// Save to YAML file
    pub fn save<P: AsRef<Path>>(data: &TuningData, path: P) -> Result<()> {
        let content = serde_yaml::to_string(data)?;
        std::fs::write(path, content)?;
        Ok(())
    }
}
