//! # Tuning Core
//! 
//! Core tuning engine with torque-centric architecture supporting universal
//! compatibility with legacy formats and modern hardware abstraction.

pub mod torque;
pub mod engine;
pub mod safety;
pub mod diagnostics;
pub mod config;

use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;

/// Core tuning engine that orchestrates all subsystems
#[derive(Debug)]
pub struct TuningEngine {
    pub id: Uuid,
    pub torque_manager: torque::TorqueManager,
    pub safety_monitor: safety::SafetyMonitor,
    pub diagnostics: diagnostics::DiagnosticsEngine,
    pub config: config::EngineConfig,
}

impl TuningEngine {
    /// Create a new tuning engine instance
    pub fn new(config: config::EngineConfig) -> Result<Self> {
        let id = Uuid::new_v4();
        
        Ok(Self {
            id,
            torque_manager: torque::TorqueManager::new(&config.torque_config)?,
            safety_monitor: safety::SafetyMonitor::new(&config.safety_config)?,
            diagnostics: diagnostics::DiagnosticsEngine::new()?,
            config,
        })
    }

    /// Process a tuning request with full safety validation
    pub async fn process_tuning_request(&mut self, request: TuningRequest) -> Result<TuningResponse> {
        tracing::info!("Processing tuning request: {:?}", request.id);

        // Safety validation first
        self.safety_monitor.validate_request(&request)?;

        // Process through torque management
        let torque_response = self.torque_manager.process_request(&request.torque_demand).await?;

        // Update diagnostics
        self.diagnostics.record_operation(&request, &torque_response).await?;

        Ok(TuningResponse {
            id: Uuid::new_v4(),
            request_id: request.id,
            torque_output: torque_response,
            timestamp: chrono::Utc::now(),
            status: ResponseStatus::Success,
        })
    }

    /// Get current engine status
    pub fn get_status(&self) -> EngineStatus {
        EngineStatus {
            engine_id: self.id,
            torque_status: self.torque_manager.get_status(),
            safety_status: self.safety_monitor.get_status(),
            diagnostics_summary: self.diagnostics.get_summary(),
            uptime: chrono::Utc::now(),
        }
    }
}

/// Tuning request structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TuningRequest {
    pub id: Uuid,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub torque_demand: torque::TorqueDemand,
    pub engine_parameters: HashMap<String, f64>,
    pub safety_overrides: Option<safety::SafetyOverrides>,
}

/// Tuning response structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TuningResponse {
    pub id: Uuid,
    pub request_id: Uuid,
    pub torque_output: torque::TorqueOutput,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub status: ResponseStatus,
}

/// Response status enumeration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ResponseStatus {
    Success,
    Warning(String),
    Error(String),
    SafetyLimited,
}

/// Engine status structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EngineStatus {
    pub engine_id: Uuid,
    pub torque_status: torque::TorqueStatus,
    pub safety_status: safety::SafetyStatus,
    pub diagnostics_summary: diagnostics::DiagnosticsSummary,
    pub uptime: chrono::DateTime<chrono::Utc>,
}

// Re-exports for convenience
pub use torque::{TorqueDemand, TorqueOutput, TorqueManager};
pub use safety::{SafetyMonitor, SafetyOverrides};
pub use diagnostics::{DiagnosticsEngine, DiagnosticsSummary};
pub use config::EngineConfig;

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_engine_creation() {
        let config = config::EngineConfig::default();
        let engine = TuningEngine::new(config);
        assert!(engine.is_ok());
    }

    #[tokio::test]
    async fn test_basic_tuning_request() {
        let config = config::EngineConfig::default();
        let mut engine = TuningEngine::new(config).unwrap();

        let request = TuningRequest {
            id: Uuid::new_v4(),
            timestamp: chrono::Utc::now(),
            torque_demand: torque::TorqueDemand::default(),
            engine_parameters: HashMap::new(),
            safety_overrides: None,
        };

        let response = engine.process_tuning_request(request).await;
        assert!(response.is_ok());
    }
}
