# Universal Tuning Software

A modern, torque-centric tuning software with universal compatibility for legacy formats and hardware abstraction for various communication protocols.

## 🎯 Core Objectives

- **Universal Compatibility**: Support 100% of legacy tuning formats (TunerStudio, MegaSquirt, rusEFI) while introducing modern alternatives
- **Bi-directional Conversion**: Seamless conversion between .msq ↔ .yaml ↔ .json with semantic preservation
- **Hardware Agnosticism**: Abstracted communication layer supporting J2534, CAN, and ESP32/WiFi bridges
- **Torque-Centric Architecture**: Modern torque management system with advanced arbitration and safety features

## 🏗️ Architecture

### Core Components

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   tuning-core   │    │   tuning-hal    │    │ tuning-formats  │
│                 │    │                 │    │                 │
│ • TuningEngine  │    │ • DeviceManager │    │ • FormatConverter│
│ • TorqueManager │    │ • J2534Support  │    │ • MSQ ↔ YAML    │
│ • SafetyMonitor │    │ • CANInterface  │    │ • JSON ↔ INI    │
│ • Diagnostics   │    │ • SerialComm    │    │ • Validation    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   tuning-web    │
                    │                 │
                    │ • WebInterface  │
                    │ • WebAssembly   │
                    │ • REST API      │
                    │ • WebSockets    │
                    └─────────────────┘
```

### Torque Management System

The core torque-centric architecture implements advanced arbitration:

```rust
struct TorqueRequest {
    driver_torque: f64,
    system_torque: f64,
    safety_limits: SafetyLimits,
}

impl TorqueArbiter {
    fn calculate_final_torque(&mut self, request: &TorqueRequest) -> Result<f64> {
        // Advanced arbitration logic with safety validation
    }
}
```

## 🚀 Quick Start

### Prerequisites

- Rust 1.70+ with Cargo
- Node.js 18+ (for web interface)
- Hardware: J2534 device, CAN interface, or serial connection

### Installation

```bash
# Clone the repository
git clone https://github.com/your-org/tuning-software.git
cd tuning-software

# Build all components
cargo build --release

# Run tests
cargo test

# Start the web interface
cargo run --bin tuning-web
```

### Basic Usage

```rust
use tuning_core::{TuningEngine, config::EngineConfig};
use tuning_hal::{DeviceManager, DeviceConfig};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize the tuning engine
    let config = EngineConfig::for_engine_type(EngineType::Turbocharged);
    let mut engine = TuningEngine::new(config)?;
    
    // Set up device communication
    let mut device_manager = DeviceManager::new();
    let device_config = DeviceConfig::j2534("path/to/j2534.dll");
    let device = tuning_hal::create_device(&device_config)?;
    device_manager.add_device("main".to_string(), device);
    device_manager.set_active_device("main")?;
    
    // Connect and start tuning
    device_manager.connect().await?;
    
    // Process tuning requests
    let request = TuningRequest {
        id: uuid::Uuid::new_v4(),
        timestamp: chrono::Utc::now(),
        torque_demand: TorqueDemand {
            driver_demand: 300.0, // Nm
            system_request: 0.0,
            safety_limits: SafetyLimits::default(),
            engine_speed: 3000.0,
            throttle_position: 50.0,
            load_factor: 0.6,
        },
        engine_parameters: HashMap::new(),
        safety_overrides: None,
    };
    
    let response = engine.process_tuning_request(request).await?;
    println!("Torque output: {:.2} Nm", response.torque_output.requested_torque);
    
    Ok(())
}
```

## 📁 File Format Support

### Supported Formats

| Format | Extension | Read | Write | Notes |
|--------|-----------|------|-------|-------|
| MegaSquirt | `.msq` | ✅ | ✅ | Full compatibility with TunerStudio |
| YAML | `.yaml`, `.yml` | ✅ | ✅ | Human-readable, version control friendly |
| JSON | `.json` | ✅ | ✅ | Web API compatible |
| **TunerStudio INI** | `.ini` | ✅ | ✅ | **Complete parser for constants, tables, dialogs** |
| **Dashboard** | `.dash` | ✅ | ✅ | **Custom gauge layouts and real-time displays** |
| **MegaLog** | `.msl`, `.csv` | ✅ | ✅ | **Data logging with analysis and visualization** |
| rusEFI | `.yaml` | ✅ | ✅ | rusEFI native format |

### Format Conversion

```rust
use tuning_formats::TuningData;

// Load from any supported format
let data = TuningData::load_from_file("tune.msq")?;

// Convert to another format
data.save_to_file("tune.yaml")?;
data.save_to_file("tune.json")?;

// Validate data integrity
let validation_report = data.validate()?;
if !validation_report.is_valid() {
    println!("Validation errors: {:?}", validation_report.errors);
}
```

## 🔧 Hardware Support

### Supported Interfaces

- **J2534**: Windows PassThru devices (OBD-II, CAN, etc.)
- **SocketCAN**: Linux CAN interfaces
- **Serial**: RS232/USB serial communication
- **TCP/UDP**: Network-based communication
- **Mock**: Testing and simulation

### Device Configuration

```rust
// J2534 device
let j2534_config = DeviceConfig::j2534("C:\\Program Files\\MyDevice\\j2534.dll");

// CAN interface
let can_config = DeviceConfig::can("can0");

// Serial device
let serial_config = DeviceConfig::serial("/dev/ttyUSB0", 115200);

// Mock device for testing
let mock_config = DeviceConfig::mock("test_device");
```

## 🌐 Web Interface

The web interface provides a modern, responsive UI for tuning operations:

- **Real-time Data**: Live engine parameters and diagnostics
- **Table Editing**: Interactive 2D/3D table editing with interpolation
- **Safety Monitoring**: Real-time safety alerts and violation tracking
- **Device Management**: Hardware connection and configuration
- **File Management**: Import/export tuning files in various formats

### Starting the Web Server

```bash
# Development mode
cargo run --bin tuning-web -- --host 127.0.0.1 --port 8080

# Production mode
cargo run --release --bin tuning-web
```

Access the interface at `http://localhost:8080`

## 🎛️ **NEW: TunerStudio Integration & MegaLog Viewer**

### TunerStudio INI File Support

The Universal Tuning Software now includes **complete TunerStudio INI file parsing**:

```rust
use tuning_formats::tunerstudio::TunerStudioParser;

// Parse TunerStudio INI file
let ini_data = TunerStudioParser::parse("megasquirt.ini")?;

// Convert to universal format
let tuning_data = TunerStudioParser::to_tuning_data(&ini_data)?;

// Access parsed data
println!("Constants: {}", ini_data.constants.len());
println!("Tables: {}", ini_data.tables.len());
println!("Gauges: {}", ini_data.gauges.len());
```

**Supported INI Sections:**
- ✅ **[Constants]** - Scalar parameters with full metadata
- ✅ **[Tables]** - 2D/3D table definitions with axes
- ✅ **[Gauges]** - Gauge configurations with ranges
- ✅ **[Dialogs]** - UI dialog definitions
- ✅ **[Menus]** - Menu structure parsing
- ✅ **[OutputChannels]** - Real-time data channels

### Dashboard File Support

Create and edit **custom dashboard layouts**:

```rust
use tuning_formats::dash::{Dashboard, DashboardParser};

// Load dashboard file
let dashboard = DashboardParser::parse("racing_dashboard.dash")?;

// Convert to web format
let web_dashboard = DashboardParser::to_web_dashboard(&dashboard)?;

// Auto-generate dashboard from tuning data
let auto_dashboard = tuning_data.to_dashboard()?;
```

**Dashboard Features:**
- 🎯 **Multiple Gauge Types**: Analog, digital, bar, LED
- ⚠️ **Warning/Danger Ranges**: Color-coded alerts
- 📊 **Real-time Graphs**: Line, bar, scatter plots
- 💡 **Smart Indicators**: Conditional warning lights
- 🎨 **Custom Styling**: Colors, fonts, layouts

### MegaLog Data Analysis

**Comprehensive log file analysis** with visualization:

```rust
use tuning_formats::megalog::MegaLogParser;

// Load MegaLog file (.msl or .csv)
let megalog = MegaLogParser::parse("datalog.msl")?;

// Analyze data
println!("Duration: {:.1}s", megalog.metadata.duration_seconds);
println!("Channels: {}", megalog.channels.len());
println!("Records: {}", megalog.data.len());

// Get specific time range
let range_data = MegaLogParser::get_time_range(&megalog, 10.0, 20.0);

// Export to CSV
MegaLogParser::export_csv(&megalog, "exported_data.csv")?;
```

**MegaLog Features:**
- 📈 **Multi-format Support**: .msl binary and .csv text
- 📊 **Statistical Analysis**: Min/max/average/std deviation
- 🔍 **Time Range Selection**: Analyze specific periods
- 📉 **Channel Filtering**: Focus on specific parameters
- 💾 **Export Capabilities**: Convert between formats

## 🔒 Safety Features

### Multi-layered Safety System

1. **Input Validation**: All tuning requests validated against safety limits
2. **Torque Arbitration**: Advanced arbitration prevents unsafe torque requests
3. **Emergency Cutoff**: Immediate shutdown capability
4. **Violation Tracking**: Comprehensive logging of safety violations
5. **Hardware Interlocks**: Integration with external safety systems

### Safety Configuration

```yaml
safety_config:
  max_torque: 500.0  # Nm
  max_rpm: 7000.0
  max_boost: 2.0     # Bar
  max_egt: 950.0     # °C
  system_armed: true
  auto_cutoff_enabled: true
```

## 🧪 Testing

```bash
# Run all tests
cargo test

# Run specific component tests
cargo test -p tuning-core
cargo test -p tuning-hal
cargo test -p tuning-formats

# Run with coverage
cargo tarpaulin --out Html
```

## 📊 Performance

- **Latency**: < 1ms for torque calculations
- **Throughput**: > 1000 requests/second
- **Memory**: < 50MB baseline usage
- **File Size**: Optimized binary formats

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Setup

```bash
# Install development dependencies
rustup component add clippy rustfmt
cargo install cargo-tarpaulin cargo-audit

# Run development checks
cargo clippy
cargo fmt
cargo audit
```

## 📄 License

This project is licensed under either of:

- Apache License, Version 2.0 ([LICENSE-APACHE](LICENSE-APACHE))
- MIT License ([LICENSE-MIT](LICENSE-MIT))

at your option.

## 🔗 Integration with Existing Projects

### rusEFI Integration
- Direct import of rusEFI configuration files
- Shared torque management libraries
- Compatible diagnostic protocols

### EpicEFI Firmware Synergy
- Shared torque request structures
- Common safety validation logic
- Unified diagnostic fault codes

### TorqueFi Open-Source Alignment
- Plugin system compatibility
- Extension model support
- Community-driven development

## 📈 Roadmap

### Phase 1: Foundation (Current)
- ✅ Core torque management system
- ✅ Hardware abstraction layer
- ✅ File format conversion
- ✅ Basic web interface

### Phase 2: Advanced Features
- [ ] Machine learning-powered tuning suggestions
- [ ] Cloud synchronization
- [ ] Mobile app companion
- [ ] Advanced diagnostics

### Phase 3: Enterprise Features
- [ ] Multi-user collaboration
- [ ] Audit logging
- [ ] Enterprise SSO
- [ ] Professional support

## 📞 Support

- **Documentation**: [docs.tuning-software.com](https://docs.tuning-software.com)
- **Community**: [Discord Server](https://discord.gg/tuning-software)
- **Issues**: [GitHub Issues](https://github.com/your-org/tuning-software/issues)
- **Email**: <EMAIL>

---

Built with ❤️ for the automotive tuning community
