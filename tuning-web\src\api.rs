//! REST API endpoints

use crate::{ApiRequest, ApiResponse, AppState};
use axum::{
    extract::{State, Json},
    http::StatusCode,
    response::<PERSON>son as ResponseJson,
};
use std::sync::Arc;

/// Handle API requests
pub async fn handle_api_request(
    State(app_state): State<Arc<AppState>>,
    Json(request): J<PERSON><ApiRequest>,
) -> Result<ResponseJson<ApiResponse>, StatusCode> {
    match request.method.as_str() {
        "get_engine_status" => handle_get_engine_status(app_state, request).await,
        "process_tuning_request" => handle_process_tuning_request(app_state, request).await,
        "get_device_list" => handle_get_device_list(app_state, request).await,
        "parse_tunerstudio_ini" => handle_parse_tunerstudio_ini(app_state, request).await,
        "load_dashboard" => handle_load_dashboard(app_state, request).await,
        "save_dashboard" => handle_save_dashboard(app_state, request).await,
        "load_megalog" => handle_load_megalog(app_state, request).await,
        "export_megalog" => handle_export_megalog(app_state, request).await,
        "convert_format" => handle_convert_format(app_state, request).await,
        _ => Ok(ResponseJson(ApiResponse::error(
            request.id,
            format!("Unknown method: {}", request.method),
        ))),
    }
}

async fn handle_get_engine_status(
    app_state: Arc<AppState>,
    request: ApiRequest,
) -> Result<ResponseJson<ApiResponse>, StatusCode> {
    match app_state.get_engine_status().await {
        Ok(status) => Ok(ResponseJson(ApiResponse::success(
            request.id,
            serde_json::to_value(status).unwrap(),
        ))),
        Err(e) => Ok(ResponseJson(ApiResponse::error(
            request.id,
            e.to_string(),
        ))),
    }
}

async fn handle_process_tuning_request(
    app_state: Arc<AppState>,
    request: ApiRequest,
) -> Result<ResponseJson<ApiResponse>, StatusCode> {
    // Parse tuning request from params
    if let Some(tuning_request_value) = request.params.get("tuning_request") {
        match serde_json::from_value::<tuning_core::TuningRequest>(tuning_request_value.clone()) {
            Ok(tuning_request) => {
                match app_state.process_tuning_request(tuning_request).await {
                    Ok(response) => Ok(ResponseJson(ApiResponse::success(
                        request.id,
                        serde_json::to_value(response).unwrap(),
                    ))),
                    Err(e) => Ok(ResponseJson(ApiResponse::error(
                        request.id,
                        e.to_string(),
                    ))),
                }
            }
            Err(e) => Ok(ResponseJson(ApiResponse::error(
                request.id,
                format!("Invalid tuning request: {}", e),
            ))),
        }
    } else {
        Ok(ResponseJson(ApiResponse::error(
            request.id,
            "Missing tuning_request parameter".to_string(),
        )))
    }
}

async fn handle_get_device_list(
    app_state: Arc<AppState>,
    request: ApiRequest,
) -> Result<ResponseJson<ApiResponse>, StatusCode> {
    let device_manager = app_state.device_manager.lock().await;
    let device_list = device_manager.list_devices();
    
    Ok(ResponseJson(ApiResponse::success(
        request.id,
        serde_json::to_value(device_list).unwrap(),
    )))
}

async fn handle_parse_tunerstudio_ini(
    _app_state: Arc<AppState>,
    request: ApiRequest,
) -> Result<ResponseJson<ApiResponse>, StatusCode> {
    if let Some(file_path_value) = request.params.get("file_path") {
        if let Ok(file_path) = serde_json::from_value::<String>(file_path_value.clone()) {
            match tuning_formats::tunerstudio::TunerStudioParser::parse(&file_path) {
                Ok(ini_data) => {
                    match tuning_formats::tunerstudio::TunerStudioParser::to_tuning_data(&ini_data) {
                        Ok(tuning_data) => Ok(ResponseJson(ApiResponse::success(
                            request.id,
                            serde_json::to_value(tuning_data).unwrap(),
                        ))),
                        Err(e) => Ok(ResponseJson(ApiResponse::error(
                            request.id,
                            format!("Failed to convert INI data: {}", e),
                        ))),
                    }
                }
                Err(e) => Ok(ResponseJson(ApiResponse::error(
                    request.id,
                    format!("Failed to parse TunerStudio INI: {}", e),
                ))),
            }
        } else {
            Ok(ResponseJson(ApiResponse::error(
                request.id,
                "Invalid file_path parameter".to_string(),
            )))
        }
    } else {
        Ok(ResponseJson(ApiResponse::error(
            request.id,
            "Missing file_path parameter".to_string(),
        )))
    }
}

async fn handle_load_dashboard(
    _app_state: Arc<AppState>,
    request: ApiRequest,
) -> Result<ResponseJson<ApiResponse>, StatusCode> {
    if let Some(file_path_value) = request.params.get("file_path") {
        if let Ok(file_path) = serde_json::from_value::<String>(file_path_value.clone()) {
            match tuning_formats::dash::DashboardParser::parse(&file_path) {
                Ok(dashboard) => {
                    match tuning_formats::dash::DashboardParser::to_web_dashboard(&dashboard) {
                        Ok(web_dashboard) => Ok(ResponseJson(ApiResponse::success(
                            request.id,
                            web_dashboard,
                        ))),
                        Err(e) => Ok(ResponseJson(ApiResponse::error(
                            request.id,
                            format!("Failed to convert dashboard: {}", e),
                        ))),
                    }
                }
                Err(e) => Ok(ResponseJson(ApiResponse::error(
                    request.id,
                    format!("Failed to load dashboard: {}", e),
                ))),
            }
        } else {
            Ok(ResponseJson(ApiResponse::error(
                request.id,
                "Invalid file_path parameter".to_string(),
            )))
        }
    } else {
        Ok(ResponseJson(ApiResponse::error(
            request.id,
            "Missing file_path parameter".to_string(),
        )))
    }
}

async fn handle_save_dashboard(
    _app_state: Arc<AppState>,
    request: ApiRequest,
) -> Result<ResponseJson<ApiResponse>, StatusCode> {
    if let (Some(file_path_value), Some(dashboard_value)) =
        (request.params.get("file_path"), request.params.get("dashboard")) {

        if let (Ok(file_path), Ok(dashboard)) = (
            serde_json::from_value::<String>(file_path_value.clone()),
            serde_json::from_value::<tuning_formats::dash::Dashboard>(dashboard_value.clone())
        ) {
            match tuning_formats::dash::DashboardParser::save(&dashboard, &file_path) {
                Ok(()) => Ok(ResponseJson(ApiResponse::success(
                    request.id,
                    serde_json::json!({"status": "saved", "path": file_path}),
                ))),
                Err(e) => Ok(ResponseJson(ApiResponse::error(
                    request.id,
                    format!("Failed to save dashboard: {}", e),
                ))),
            }
        } else {
            Ok(ResponseJson(ApiResponse::error(
                request.id,
                "Invalid parameters".to_string(),
            )))
        }
    } else {
        Ok(ResponseJson(ApiResponse::error(
            request.id,
            "Missing file_path or dashboard parameter".to_string(),
        )))
    }
}

async fn handle_load_megalog(
    _app_state: Arc<AppState>,
    request: ApiRequest,
) -> Result<ResponseJson<ApiResponse>, StatusCode> {
    if let Some(file_path_value) = request.params.get("file_path") {
        if let Ok(file_path) = serde_json::from_value::<String>(file_path_value.clone()) {
            match tuning_formats::megalog::MegaLogParser::parse(&file_path) {
                Ok(megalog) => Ok(ResponseJson(ApiResponse::success(
                    request.id,
                    serde_json::to_value(megalog).unwrap(),
                ))),
                Err(e) => Ok(ResponseJson(ApiResponse::error(
                    request.id,
                    format!("Failed to load MegaLog: {}", e),
                ))),
            }
        } else {
            Ok(ResponseJson(ApiResponse::error(
                request.id,
                "Invalid file_path parameter".to_string(),
            )))
        }
    } else {
        Ok(ResponseJson(ApiResponse::error(
            request.id,
            "Missing file_path parameter".to_string(),
        )))
    }
}

async fn handle_export_megalog(
    _app_state: Arc<AppState>,
    request: ApiRequest,
) -> Result<ResponseJson<ApiResponse>, StatusCode> {
    if let (Some(megalog_value), Some(file_path_value)) =
        (request.params.get("megalog"), request.params.get("file_path")) {

        if let (Ok(megalog), Ok(file_path)) = (
            serde_json::from_value::<tuning_formats::megalog::MegaLog>(megalog_value.clone()),
            serde_json::from_value::<String>(file_path_value.clone())
        ) {
            match tuning_formats::megalog::MegaLogParser::export_csv(&megalog, &file_path) {
                Ok(()) => Ok(ResponseJson(ApiResponse::success(
                    request.id,
                    serde_json::json!({"status": "exported", "path": file_path}),
                ))),
                Err(e) => Ok(ResponseJson(ApiResponse::error(
                    request.id,
                    format!("Failed to export MegaLog: {}", e),
                ))),
            }
        } else {
            Ok(ResponseJson(ApiResponse::error(
                request.id,
                "Invalid parameters".to_string(),
            )))
        }
    } else {
        Ok(ResponseJson(ApiResponse::error(
            request.id,
            "Missing megalog or file_path parameter".to_string(),
        )))
    }
}

async fn handle_convert_format(
    _app_state: Arc<AppState>,
    request: ApiRequest,
) -> Result<ResponseJson<ApiResponse>, StatusCode> {
    if let (Some(input_path_value), Some(output_path_value)) =
        (request.params.get("input_path"), request.params.get("output_path")) {

        if let (Ok(input_path), Ok(output_path)) = (
            serde_json::from_value::<String>(input_path_value.clone()),
            serde_json::from_value::<String>(output_path_value.clone())
        ) {
            match tuning_formats::converter::FormatConverter::convert(&input_path, &output_path) {
                Ok(()) => Ok(ResponseJson(ApiResponse::success(
                    request.id,
                    serde_json::json!({
                        "status": "converted",
                        "input": input_path,
                        "output": output_path
                    }),
                ))),
                Err(e) => Ok(ResponseJson(ApiResponse::error(
                    request.id,
                    format!("Failed to convert format: {}", e),
                ))),
            }
        } else {
            Ok(ResponseJson(ApiResponse::error(
                request.id,
                "Invalid parameters".to_string(),
            )))
        }
    } else {
        Ok(ResponseJson(ApiResponse::error(
            request.id,
            "Missing input_path or output_path parameter".to_string(),
        )))
    }
}
