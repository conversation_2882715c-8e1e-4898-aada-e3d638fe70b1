//! REST API endpoints

use crate::{ApiRequest, ApiResponse, AppState};
use axum::{
    extract::{State, Json},
    http::StatusCode,
    response::Json as ResponseJson,
};
use std::sync::Arc;

/// Handle API requests
pub async fn handle_api_request(
    State(app_state): State<Arc<AppState>>,
    Json(request): Json<ApiRequest>,
) -> Result<ResponseJson<ApiResponse>, StatusCode> {
    match request.method.as_str() {
        "get_engine_status" => handle_get_engine_status(app_state, request).await,
        "process_tuning_request" => handle_process_tuning_request(app_state, request).await,
        "get_device_list" => handle_get_device_list(app_state, request).await,
        _ => Ok(ResponseJson(ApiResponse::error(
            request.id,
            format!("Unknown method: {}", request.method),
        ))),
    }
}

async fn handle_get_engine_status(
    app_state: Arc<AppState>,
    request: ApiRequest,
) -> Result<ResponseJson<ApiResponse>, StatusCode> {
    match app_state.get_engine_status().await {
        Ok(status) => Ok(ResponseJson(ApiResponse::success(
            request.id,
            serde_json::to_value(status).unwrap(),
        ))),
        Err(e) => Ok(ResponseJson(ApiResponse::error(
            request.id,
            e.to_string(),
        ))),
    }
}

async fn handle_process_tuning_request(
    app_state: Arc<AppState>,
    request: ApiRequest,
) -> Result<ResponseJson<ApiResponse>, StatusCode> {
    // Parse tuning request from params
    if let Some(tuning_request_value) = request.params.get("tuning_request") {
        match serde_json::from_value::<tuning_core::TuningRequest>(tuning_request_value.clone()) {
            Ok(tuning_request) => {
                match app_state.process_tuning_request(tuning_request).await {
                    Ok(response) => Ok(ResponseJson(ApiResponse::success(
                        request.id,
                        serde_json::to_value(response).unwrap(),
                    ))),
                    Err(e) => Ok(ResponseJson(ApiResponse::error(
                        request.id,
                        e.to_string(),
                    ))),
                }
            }
            Err(e) => Ok(ResponseJson(ApiResponse::error(
                request.id,
                format!("Invalid tuning request: {}", e),
            ))),
        }
    } else {
        Ok(ResponseJson(ApiResponse::error(
            request.id,
            "Missing tuning_request parameter".to_string(),
        )))
    }
}

async fn handle_get_device_list(
    app_state: Arc<AppState>,
    request: ApiRequest,
) -> Result<ResponseJson<ApiResponse>, StatusCode> {
    let device_manager = app_state.device_manager.lock().await;
    let device_list = device_manager.list_devices();
    
    Ok(ResponseJson(ApiResponse::success(
        request.id,
        serde_json::to_value(device_list).unwrap(),
    )))
}
