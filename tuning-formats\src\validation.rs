//! Tuning data validation

use crate::TuningData;
use anyhow::Result;
use serde::{Deserialize, Serialize};

/// Validation report
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationReport {
    pub is_valid: bool,
    pub errors: Vec<ValidationError>,
    pub warnings: Vec<ValidationWarning>,
}

impl ValidationReport {
    pub fn new() -> Self {
        Self {
            is_valid: true,
            errors: Vec::new(),
            warnings: Vec::new(),
        }
    }

    pub fn is_valid(&self) -> bool {
        self.is_valid && self.errors.is_empty()
    }

    pub fn add_error(&mut self, error: ValidationError) {
        self.errors.push(error);
        self.is_valid = false;
    }

    pub fn add_warning(&mut self, warning: ValidationWarning) {
        self.warnings.push(warning);
    }
}

/// Validation error
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationError {
    pub field: String,
    pub message: String,
    pub severity: ErrorSeverity,
}

/// Validation warning
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ValidationWarning {
    pub field: String,
    pub message: String,
}

/// Error severity
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ErrorSeverity {
    Critical,
    Major,
    Minor,
}

/// Tuning data validator
pub struct TuningValidator;

impl TuningValidator {
    /// Validate tuning data
    pub fn validate(data: &TuningData) -> Result<ValidationReport> {
        let mut report = ValidationReport::new();

        // Validate engine configuration
        Self::validate_engine_config(data, &mut report);

        // Validate tables
        Self::validate_tables(data, &mut report);

        // Validate scalar values
        Self::validate_scalars(data, &mut report);

        Ok(report)
    }

    fn validate_engine_config(data: &TuningData, report: &mut ValidationReport) {
        if data.engine_config.displacement <= 0.0 {
            report.add_error(ValidationError {
                field: "engine_config.displacement".to_string(),
                message: "Engine displacement must be positive".to_string(),
                severity: ErrorSeverity::Critical,
            });
        }

        if data.engine_config.cylinders == 0 {
            report.add_error(ValidationError {
                field: "engine_config.cylinders".to_string(),
                message: "Number of cylinders must be positive".to_string(),
                severity: ErrorSeverity::Critical,
            });
        }

        if data.engine_config.compression_ratio < 6.0 || data.engine_config.compression_ratio > 20.0 {
            report.add_warning(ValidationWarning {
                field: "engine_config.compression_ratio".to_string(),
                message: "Compression ratio seems unusual (typical range: 6-20)".to_string(),
            });
        }
    }

    fn validate_tables(data: &TuningData, report: &mut ValidationReport) {
        // Validate fuel tables
        for (name, table) in &data.fuel_tables {
            if table.data.is_empty() {
                report.add_error(ValidationError {
                    field: format!("fuel_tables.{}", name),
                    message: "Table has no data".to_string(),
                    severity: ErrorSeverity::Major,
                });
            }

            // Check for reasonable fuel values
            for (y, row) in table.data.iter().enumerate() {
                for (x, &value) in row.iter().enumerate() {
                    if value < 0.0 || value > 50.0 {
                        report.add_warning(ValidationWarning {
                            field: format!("fuel_tables.{}.data[{}][{}]", name, y, x),
                            message: format!("Fuel value {} seems unusual", value),
                        });
                    }
                }
            }
        }

        // Validate ignition tables
        for (name, table) in &data.ignition_tables {
            if table.data.is_empty() {
                report.add_error(ValidationError {
                    field: format!("ignition_tables.{}", name),
                    message: "Table has no data".to_string(),
                    severity: ErrorSeverity::Major,
                });
            }

            // Check for reasonable ignition timing values
            for (y, row) in table.data.iter().enumerate() {
                for (x, &value) in row.iter().enumerate() {
                    if value < -10.0 || value > 50.0 {
                        report.add_warning(ValidationWarning {
                            field: format!("ignition_tables.{}.data[{}][{}]", name, y, x),
                            message: format!("Ignition timing {} seems unusual", value),
                        });
                    }
                }
            }
        }
    }

    fn validate_scalars(data: &TuningData, report: &mut ValidationReport) {
        for (name, scalar) in &data.scalar_values {
            // Check if value is within specified bounds
            if let Some(min) = scalar.min_value {
                if scalar.value < min {
                    report.add_error(ValidationError {
                        field: format!("scalar_values.{}", name),
                        message: format!("Value {} is below minimum {}", scalar.value, min),
                        severity: ErrorSeverity::Major,
                    });
                }
            }

            if let Some(max) = scalar.max_value {
                if scalar.value > max {
                    report.add_error(ValidationError {
                        field: format!("scalar_values.{}", name),
                        message: format!("Value {} is above maximum {}", scalar.value, max),
                        severity: ErrorSeverity::Major,
                    });
                }
            }
        }
    }
}
