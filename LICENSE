MIT License

Copyright (c) 2024 Universal Tuning Software Contributors

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

---

IMPORTANT SAFETY NOTICE:

This software is designed to interface with and control automotive engine 
management systems. The use of this software involves inherent risks including 
but not limited to:

- Engine damage
- Vehicle damage  
- Personal injury
- Property damage
- Emissions compliance violations
- Warranty voidance

BY USING THIS SOFTWARE, YOU ACKNOWLEDGE AND ACCEPT THESE RISKS.

The contributors and maintainers of this software:

1. PROVIDE NO WARRANTY of any kind, express or implied
2. ACCEPT NO LIABILITY for any damages resulting from use of this software
3. STRONGLY RECOMMEND professional tuning expertise and proper safety precautions
4. ADVISE users to thoroughly understand their engine management systems
5. RECOMMEND extensive testing in controlled environments

Users are solely responsible for:

- Understanding their engine management systems
- Implementing appropriate safety measures
- Complying with local laws and regulations
- Ensuring proper installation and configuration
- Testing thoroughly before operational use
- Maintaining backups of original calibrations

This software is intended for:

- Educational purposes
- Research and development
- Professional tuning applications by qualified individuals
- Enthusiast use by knowledgeable users who accept full responsibility

This software is NOT intended for:

- Production vehicles without proper validation
- Use by inexperienced individuals
- Applications where safety is not properly considered
- Violation of emissions regulations or local laws

ALWAYS:

- Maintain original calibration backups
- Test changes incrementally
- Monitor engine parameters continuously
- Have safety systems in place
- Consult with experienced professionals when in doubt

The automotive tuning community values safety, responsibility, and continuous 
learning. Please use this software responsibly and contribute to the community's 
knowledge base while prioritizing safety above all else.

For questions about safe usage, consult with experienced tuners, engine 
builders, or automotive professionals in your area.
