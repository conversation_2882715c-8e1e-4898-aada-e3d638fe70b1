[package]
name = "tuning-hal"
version = "0.1.0"
edition = "2021"
description = "Hardware Abstraction Layer for tuning devices"
license = "MIT OR Apache-2.0"

[dependencies]
# Workspace dependencies
tokio = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
anyhow = { workspace = true }
thiserror = { workspace = true }
tracing = { workspace = true }

# HAL-specific dependencies
async-trait = "0.1"
bytes = "1.0"

# Serial communication
serialport = { workspace = true, optional = true }

# CAN communication
socketcan = { workspace = true, optional = true }

# J2534 support (Windows-specific)
[target.'cfg(windows)'.dependencies]
winapi = { version = "0.3", features = ["winbase", "handleapi", "fileapi"] }
libloading = "0.8"

[features]
default = ["serial", "can"]
serial = ["serialport"]
can = ["socketcan"]
j2534 = []
mock = []
