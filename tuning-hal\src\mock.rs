//! Mock device for testing

use crate::{Devi<PERSON>Interface, DeviceInfo, Device<PERSON>apabilities, DeviceConfig, ProtocolMessage, DeviceType, ProtocolType};
use anyhow::Result;
use async_trait::async_trait;
use std::time::Duration;

/// Mock device implementation for testing
#[derive(Debug)]
pub struct MockDevice {
    info: DeviceInfo,
    capabilities: DeviceCapabilities,
    connected: bool,
    message_queue: Vec<ProtocolMessage>,
}

impl MockDevice {
    pub fn new(config: &DeviceConfig) -> Result<Self> {
        let info = DeviceInfo {
            name: format!("Mock Device ({})", config.connection_string),
            device_type: DeviceType::Mock,
            manufacturer: "Test".to_string(),
            model: "Mock".to_string(),
            serial_number: config.connection_string.clone(),
            firmware_version: "1.0.0".to_string(),
            hardware_version: "1.0.0".to_string(),
        };

        let capabilities = DeviceCapabilities {
            supported_protocols: vec![
                ProtocolType::CAN,
                ProtocolType::Serial,
                ProtocolType::J2534,
            ],
            max_baud_rate: 1000000,
            supports_can_fd: true,
            supports_j1850: true,
            supports_iso9141: true,
            supports_kwp2000: true,
            supports_iso14230: true,
            buffer_size: 4096,
        };

        Ok(Self {
            info,
            capabilities,
            connected: false,
            message_queue: Vec::new(),
        })
    }

    /// Add a message to the mock device's receive queue
    pub fn add_mock_message(&mut self, message: ProtocolMessage) {
        self.message_queue.push(message);
    }

    /// Clear the mock message queue
    pub fn clear_mock_messages(&mut self) {
        self.message_queue.clear();
    }
}

#[async_trait]
impl DeviceInterface for MockDevice {
    async fn connect(&mut self) -> Result<()> {
        self.connected = true;
        tracing::info!("Mock device connected: {}", self.info.name);
        Ok(())
    }

    async fn disconnect(&mut self) -> Result<()> {
        self.connected = false;
        tracing::info!("Mock device disconnected: {}", self.info.name);
        Ok(())
    }

    fn is_connected(&self) -> bool {
        self.connected
    }

    async fn send_raw(&mut self, data: &[u8]) -> Result<()> {
        tracing::debug!("Mock device sent {} bytes: {:02x?}", data.len(), data);
        Ok(())
    }

    async fn receive_raw(&mut self, _timeout: Duration) -> Result<Vec<u8>> {
        // Return some mock data
        Ok(vec![0x01, 0x02, 0x03, 0x04])
    }

    async fn send_message(&mut self, message: &ProtocolMessage) -> Result<()> {
        tracing::debug!("Mock device sent message: {:?}", message);
        Ok(())
    }

    async fn receive_message(&mut self, _timeout: Duration) -> Result<ProtocolMessage> {
        if let Some(message) = self.message_queue.pop() {
            Ok(message)
        } else {
            // Return a default mock message
            Ok(ProtocolMessage::can_message(
                0x7E8,
                vec![0x03, 0x41, 0x0C, 0x1A, 0xF8],
                crate::MessageDirection::Receive,
            ))
        }
    }

    fn get_device_info(&self) -> &DeviceInfo {
        &self.info
    }

    fn get_capabilities(&self) -> &DeviceCapabilities {
        &self.capabilities
    }
}
