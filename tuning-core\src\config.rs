//! # Configuration Management
//! 
//! Configuration structures and management for the tuning engine.

use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::path::Path;
use crate::torque::TorqueConfig;
use crate::safety::SafetyConfig;
use crate::diagnostics::DiagnosticsConfig;

/// Main engine configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EngineConfig {
    pub engine_info: EngineInfo,
    pub torque_config: TorqueConfig,
    pub safety_config: SafetyConfig,
    pub diagnostics_config: DiagnosticsConfig,
    pub communication_config: CommunicationConfig,
    pub logging_config: LoggingConfig,
}

impl Default for EngineConfig {
    fn default() -> Self {
        Self {
            engine_info: EngineInfo::default(),
            torque_config: TorqueConfig {
                fuel_config: crate::torque::FuelConfig {
                    torque_to_fuel_ratio: 0.1,
                    max_pulse_width: 20.0,
                    base_fuel_pressure: 3.0,
                },
                ignition_config: crate::torque::IgnitionConfig {
                    base_timing: 10.0,
                    torque_timing_factor: 0.02,
                    base_dwell: 3.0,
                },
                max_torque_rate: 100.0,
            },
            safety_config: SafetyConfig::default(),
            diagnostics_config: DiagnosticsConfig::default(),
            communication_config: CommunicationConfig::default(),
            logging_config: LoggingConfig::default(),
        }
    }
}

impl EngineConfig {
    /// Load configuration from file
    pub fn load_from_file<P: AsRef<Path>>(path: P) -> Result<Self> {
        let content = std::fs::read_to_string(path)?;
        let config: EngineConfig = serde_yaml::from_str(&content)?;
        Ok(config)
    }

    /// Save configuration to file
    pub fn save_to_file<P: AsRef<Path>>(&self, path: P) -> Result<()> {
        let content = serde_yaml::to_string(self)?;
        std::fs::write(path, content)?;
        Ok(())
    }

    /// Load configuration from JSON
    pub fn load_from_json<P: AsRef<Path>>(path: P) -> Result<Self> {
        let content = std::fs::read_to_string(path)?;
        let config: EngineConfig = serde_json::from_str(&content)?;
        Ok(config)
    }

    /// Save configuration to JSON
    pub fn save_to_json<P: AsRef<Path>>(&self, path: P) -> Result<()> {
        let content = serde_json::to_string_pretty(self)?;
        std::fs::write(path, content)?;
        Ok(())
    }

    /// Validate configuration
    pub fn validate(&self) -> Result<()> {
        // Validate engine info
        if self.engine_info.displacement <= 0.0 {
            return Err(anyhow::anyhow!("Engine displacement must be positive"));
        }

        if self.engine_info.cylinders == 0 {
            return Err(anyhow::anyhow!("Number of cylinders must be positive"));
        }

        // Validate torque config
        if self.torque_config.fuel_config.max_pulse_width <= 0.0 {
            return Err(anyhow::anyhow!("Max pulse width must be positive"));
        }

        // Validate safety config
        if self.safety_config.max_torque <= 0.0 {
            return Err(anyhow::anyhow!("Max torque must be positive"));
        }

        if self.safety_config.max_rpm <= 0.0 {
            return Err(anyhow::anyhow!("Max RPM must be positive"));
        }

        Ok(())
    }

    /// Create a configuration for a specific engine type
    pub fn for_engine_type(engine_type: EngineType) -> Self {
        let mut config = Self::default();
        
        match engine_type {
            EngineType::NaturallyAspirated => {
                config.safety_config.max_boost = 0.0;
                config.torque_config.fuel_config.torque_to_fuel_ratio = 0.08;
            }
            EngineType::Turbocharged => {
                config.safety_config.max_boost = 2.5;
                config.torque_config.fuel_config.torque_to_fuel_ratio = 0.12;
            }
            EngineType::Supercharged => {
                config.safety_config.max_boost = 1.8;
                config.torque_config.fuel_config.torque_to_fuel_ratio = 0.11;
            }
            EngineType::Hybrid => {
                config.safety_config.max_torque = 800.0;
                config.torque_config.fuel_config.torque_to_fuel_ratio = 0.06;
            }
        }

        config
    }
}

/// Engine information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EngineInfo {
    pub make: String,
    pub model: String,
    pub year: u16,
    pub displacement: f64, // Liters
    pub cylinders: u8,
    pub engine_type: EngineType,
    pub fuel_type: FuelType,
    pub compression_ratio: f64,
}

impl Default for EngineInfo {
    fn default() -> Self {
        Self {
            make: "Generic".to_string(),
            model: "Engine".to_string(),
            year: 2024,
            displacement: 2.0,
            cylinders: 4,
            engine_type: EngineType::NaturallyAspirated,
            fuel_type: FuelType::Gasoline,
            compression_ratio: 10.0,
        }
    }
}

/// Engine types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum EngineType {
    NaturallyAspirated,
    Turbocharged,
    Supercharged,
    Hybrid,
}

/// Fuel types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FuelType {
    Gasoline,
    Diesel,
    E85,
    Methanol,
    RacingFuel,
}

/// Communication configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CommunicationConfig {
    pub protocol: CommunicationProtocol,
    pub device_path: String,
    pub baud_rate: u32,
    pub timeout_ms: u32,
    pub retry_attempts: u8,
}

impl Default for CommunicationConfig {
    fn default() -> Self {
        Self {
            protocol: CommunicationProtocol::J2534,
            device_path: "/dev/ttyUSB0".to_string(),
            baud_rate: 115200,
            timeout_ms: 1000,
            retry_attempts: 3,
        }
    }
}

/// Communication protocols
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum CommunicationProtocol {
    J2534,
    CAN,
    Serial,
    TCP,
    UDP,
}

/// Logging configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoggingConfig {
    pub level: LogLevel,
    pub log_to_file: bool,
    pub log_file_path: String,
    pub max_log_size_mb: u32,
    pub log_rotation_count: u8,
}

impl Default for LoggingConfig {
    fn default() -> Self {
        Self {
            level: LogLevel::Info,
            log_to_file: true,
            log_file_path: "tuning.log".to_string(),
            max_log_size_mb: 10,
            log_rotation_count: 5,
        }
    }
}

/// Log levels
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum LogLevel {
    Error,
    Warn,
    Info,
    Debug,
    Trace,
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::NamedTempFile;

    #[test]
    fn test_default_config_validation() {
        let config = EngineConfig::default();
        assert!(config.validate().is_ok());
    }

    #[test]
    fn test_config_serialization() {
        let config = EngineConfig::default();
        let yaml = serde_yaml::to_string(&config);
        assert!(yaml.is_ok());

        let json = serde_json::to_string(&config);
        assert!(json.is_ok());
    }

    #[test]
    fn test_config_file_operations() {
        let config = EngineConfig::default();
        
        // Test YAML file operations
        let temp_file = NamedTempFile::new().unwrap();
        let result = config.save_to_file(temp_file.path());
        assert!(result.is_ok());

        let loaded_config = EngineConfig::load_from_file(temp_file.path());
        assert!(loaded_config.is_ok());
    }

    #[test]
    fn test_engine_type_configs() {
        let turbo_config = EngineConfig::for_engine_type(EngineType::Turbocharged);
        assert!(turbo_config.safety_config.max_boost > 0.0);

        let na_config = EngineConfig::for_engine_type(EngineType::NaturallyAspirated);
        assert_eq!(na_config.safety_config.max_boost, 0.0);
    }

    #[test]
    fn test_invalid_config_validation() {
        let mut config = EngineConfig::default();
        config.engine_info.displacement = -1.0;
        
        let result = config.validate();
        assert!(result.is_err());
    }
}
