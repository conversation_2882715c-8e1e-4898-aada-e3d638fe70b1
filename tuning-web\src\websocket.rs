//! WebSocket handling

use crate::{AppState, WebSocketClient, WebSocketMessage};
use futures_util::{SinkExt, StreamExt};
use axum::{
    extract::{
        ws::{Message, WebSocket},
        State, WebSocketUpgrade,
    },
    response::Response,
};
use std::sync::Arc;
use tokio::sync::mpsc;
use uuid::Uuid;

/// Handle WebSocket upgrade
pub async fn websocket_handler(
    ws: WebSocketUpgrade,
    State(app_state): State<Arc<AppState>>,
) -> Response {
    ws.on_upgrade(move |socket| handle_websocket(socket, app_state))
}

/// Handle WebSocket connection
async fn handle_websocket(socket: WebSocket, app_state: Arc<AppState>) {
    let client_id = Uuid::new_v4().to_string();
    let (sender, mut receiver) = socket.split();
    let (tx, mut rx) = mpsc::unbounded_channel::<String>();

    // Create client
    let client = WebSocketClient::new(client_id.clone(), tx);
    app_state.add_websocket_client(client).await;

    tracing::info!("WebSocket client connected: {}", client_id);

    // Spawn task to handle outgoing messages
    let app_state_clone = app_state.clone();
    let client_id_clone = client_id.clone();
    let send_task = tokio::spawn(async move {
        let mut sender = sender;
        while let Some(msg) = rx.recv().await {
            if sender.send(Message::Text(msg)).await.is_err() {
                break;
            }
        }

        // Remove client when send task ends
        app_state_clone.remove_websocket_client(&client_id_clone).await;
    });

    // Handle incoming messages
    let app_state_clone = app_state.clone();
    let client_id_clone = client_id.clone();
    let receive_task = tokio::spawn(async move {
        while let Some(msg) = receiver.next().await {
            match msg {
                Ok(Message::Text(text)) => {
                    if let Err(e) = handle_websocket_message(&text, &app_state_clone).await {
                        tracing::error!("Error handling WebSocket message: {}", e);
                    }
                }
                Ok(Message::Close(_)) => {
                    tracing::info!("WebSocket client disconnected: {}", client_id_clone);
                    break;
                }
                Err(e) => {
                    tracing::error!("WebSocket error: {}", e);
                    break;
                }
                _ => {}
            }
        }
    });

    // Wait for either task to complete
    tokio::select! {
        _ = send_task => {},
        _ = receive_task => {},
    }

    // Clean up
    app_state.remove_websocket_client(&client_id).await;
    tracing::info!("WebSocket client cleaned up: {}", client_id);
}

/// Handle incoming WebSocket message
async fn handle_websocket_message(
    message: &str,
    app_state: &Arc<AppState>,
) -> anyhow::Result<()> {
    let ws_message: WebSocketMessage = serde_json::from_str(message)?;

    match ws_message {
        WebSocketMessage::TorqueRequest { request } => {
            // Process the torque request
            match app_state.process_tuning_request(request).await {
                Ok(response) => {
                    let response_message = WebSocketMessage::TorqueResponse { response };
                    app_state.broadcast_message(&response_message).await?;
                }
                Err(e) => {
                    let error_message = WebSocketMessage::Error {
                        message: e.to_string(),
                    };
                    app_state.broadcast_message(&error_message).await?;
                }
            }
        }
        _ => {
            tracing::debug!("Received WebSocket message: {:?}", ws_message);
        }
    }

    Ok(())
}
