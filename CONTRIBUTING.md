# Contributing to Universal Tuning Software

Thank you for your interest in contributing to the Universal Tuning Software project! This document provides guidelines and information for contributors.

## 🎯 Project Overview

Universal Tuning Software is a modern, torque-centric ECU tuning platform built in Rust with comprehensive format support and a web-based interface. Our goal is to create a universal platform that bridges different tuning ecosystems while maintaining safety and performance.

## 🏗️ Architecture

The project is organized as a Rust workspace with four main crates:

- **`tuning-core`** - Core torque management and safety systems
- **`tuning-formats`** - File format parsers and converters
- **`tuning-hal`** - Hardware abstraction layer for ECU communication
- **`tuning-web`** - Web server and API endpoints

## 🚀 Getting Started

### Prerequisites

- Rust 1.70+ (latest stable recommended)
- Git
- A modern web browser for testing the interface

### Development Setup

1. **Clone the repository:**
   ```bash
   git clone https://github.com/dadslands/universal-tuning-software.git
   cd universal-tuning-software
   ```

2. **Build the project:**
   ```bash
   cargo build
   ```

3. **Run tests:**
   ```bash
   cargo test
   ```

4. **Start the development server:**
   ```bash
   cargo run
   ```

5. **Access the web interface:**
   Open `http://localhost:8080` in your browser

### Quick Development Scripts

We provide several batch scripts for Windows development:

- `build.bat` - Build the project
- `run.bat` - Start the application
- `test.bat` - Run all tests
- `complete-test.bat` - Full test suite with examples

## 📋 How to Contribute

### 1. Issues and Bug Reports

- Check existing issues before creating new ones
- Use the issue templates when available
- Provide detailed reproduction steps for bugs
- Include system information and error messages

### 2. Feature Requests

- Describe the use case and benefits
- Consider backward compatibility
- Discuss implementation approach if you have ideas

### 3. Code Contributions

#### Pull Request Process

1. **Fork the repository** and create a feature branch
2. **Make your changes** following our coding standards
3. **Add tests** for new functionality
4. **Update documentation** as needed
5. **Test thoroughly** using our test scripts
6. **Submit a pull request** with a clear description

#### Coding Standards

**Rust Code:**
- Follow `rustfmt` formatting (run `cargo fmt`)
- Address `clippy` warnings (run `cargo clippy`)
- Write comprehensive tests for new features
- Document public APIs with doc comments
- Use meaningful variable and function names

**Web Interface:**
- Follow existing HTML/CSS/JavaScript patterns
- Ensure responsive design
- Test across different browsers
- Maintain accessibility standards

#### Commit Messages

Use clear, descriptive commit messages:

```
feat: add TunerStudio INI parser for constants section
fix: resolve dashboard gauge display issue
docs: update API documentation for format conversion
test: add unit tests for MegaLog parser
```

## 🎛️ Key Areas for Contribution

### 1. Format Support

We're always looking to expand format support:

- **TunerStudio formats** - Enhanced .ini parsing, .dash improvements
- **ECU-specific formats** - Haltech, AEM, Hondata, etc.
- **Data logging formats** - Additional log file types
- **Conversion utilities** - Between different formats

### 2. Hardware Support

Help us support more ECU interfaces:

- **J2534 devices** - PassThru interface implementations
- **CAN bus protocols** - Additional CAN implementations
- **Serial protocols** - UART/RS232 communication
- **USB devices** - Direct USB ECU communication

### 3. Web Interface

Improve the user experience:

- **Dashboard editor** - Visual gauge layout tools
- **Data visualization** - Charts and graphs for log analysis
- **Real-time monitoring** - Live data display improvements
- **Mobile responsiveness** - Better mobile device support

### 4. Core Engine

Enhance the torque management system:

- **Safety algorithms** - Advanced protection systems
- **Calibration tools** - Automated tuning assistance
- **Performance optimization** - Speed and memory improvements
- **Testing frameworks** - Simulation and validation tools

## 🧪 Testing

### Running Tests

```bash
# Run all tests
cargo test

# Run tests for specific crate
cargo test -p tuning-formats

# Run with output
cargo test -- --nocapture

# Run integration tests
cargo test --test integration
```

### Test Coverage

We aim for high test coverage, especially for:

- File format parsers (critical for data integrity)
- Safety systems (essential for engine protection)
- API endpoints (important for reliability)
- Conversion utilities (data accuracy)

### Example Files

Test your changes with the provided example files:

- `examples/megasquirt.ini` - TunerStudio INI format
- `examples/racing_dashboard.dash` - Dashboard configuration
- `examples/config.yaml` - Engine configuration

## 📚 Documentation

### Code Documentation

- Document all public APIs with rustdoc comments
- Include examples in documentation
- Explain complex algorithms and safety considerations
- Update README.md for significant changes

### User Documentation

- Update GETTING_STARTED.md for new features
- Add examples for new functionality
- Document configuration options
- Provide troubleshooting guides

## 🔒 Security and Safety

This software controls engine parameters, so safety is paramount:

- **Never bypass safety checks** without explicit review
- **Validate all input data** thoroughly
- **Test safety systems** extensively
- **Document safety implications** of changes

### Safety Review Process

Changes affecting engine safety require:

1. Detailed safety analysis
2. Comprehensive testing
3. Review by experienced contributors
4. Documentation of safety considerations

## 🌟 Recognition

Contributors are recognized in:

- README.md contributors section
- Release notes for significant contributions
- GitHub contributor statistics
- Special recognition for major features

## 📞 Getting Help

- **GitHub Issues** - For bugs and feature requests
- **GitHub Discussions** - For questions and general discussion
- **Code Review** - Request reviews on pull requests
- **Documentation** - Check existing docs first

## 📄 License

By contributing to Universal Tuning Software, you agree that your contributions will be licensed under the same license as the project.

## 🙏 Thank You

Every contribution helps make Universal Tuning Software better for the entire automotive tuning community. Whether you're fixing a typo, adding a feature, or improving documentation, your help is appreciated!

---

**Happy Tuning!** 🏁
