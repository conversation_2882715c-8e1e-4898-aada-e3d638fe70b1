//! TunerStudio INI file parser and processor
//! 
//! Supports parsing TunerStudio .ini files which define ECU configurations,
//! table definitions, and dialog layouts.

use crate::{TuningData, Table2D, Axis, ScalarValue, Constant, ConstantValue};
use anyhow::{Result, anyhow};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::path::Path;
use regex::Regex;

/// TunerStudio INI file structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TunerStudioIni {
    pub metadata: IniMetadata,
    pub constants: HashMap<String, IniConstant>,
    pub tables: HashMap<String, IniTable>,
    pub dialogs: HashMap<String, IniDialog>,
    pub menus: HashMap<String, IniMenu>,
    pub curves: HashMap<String, IniCurve>,
    pub gauges: HashMap<String, IniGauge>,
}

/// INI file metadata
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct IniMetadata {
    pub signature: String,
    pub version: String,
    pub query_command: String,
    pub ecu_name: String,
    pub firmware_version: String,
}

/// INI constant definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IniConstant {
    pub name: String,
    pub data_type: IniDataType,
    pub offset: u32,
    pub units: String,
    pub scale: f64,
    pub translate: f64,
    pub min_value: Option<f64>,
    pub max_value: Option<f64>,
    pub digits: u8,
}

/// INI table definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IniTable {
    pub name: String,
    pub x_axis: IniAxis,
    pub y_axis: IniAxis,
    pub z_axis: IniAxis,
    pub x_size: usize,
    pub y_size: usize,
    pub x_bins: Vec<String>,
    pub y_bins: Vec<String>,
}

/// INI axis definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IniAxis {
    pub name: String,
    pub units: String,
    pub data_type: IniDataType,
    pub scale: f64,
    pub translate: f64,
}

/// INI dialog definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IniDialog {
    pub name: String,
    pub title: String,
    pub fields: Vec<IniField>,
    pub panels: Vec<IniPanel>,
}

/// INI field definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IniField {
    pub name: String,
    pub field_type: IniFieldType,
    pub variable: String,
    pub title: String,
}

/// INI panel definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IniPanel {
    pub name: String,
    pub title: String,
    pub fields: Vec<String>,
}

/// INI menu definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IniMenu {
    pub name: String,
    pub title: String,
    pub items: Vec<IniMenuItem>,
}

/// INI menu item
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IniMenuItem {
    pub name: String,
    pub title: String,
    pub action: String,
}

/// INI curve definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IniCurve {
    pub name: String,
    pub title: String,
    pub x_axis: String,
    pub y_axis: String,
    pub x_bins: Vec<String>,
    pub y_bins: Vec<String>,
}

/// INI gauge definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IniGauge {
    pub name: String,
    pub title: String,
    pub variable: String,
    pub units: String,
    pub min_value: f64,
    pub max_value: f64,
    pub warning_low: Option<f64>,
    pub warning_high: Option<f64>,
    pub danger_low: Option<f64>,
    pub danger_high: Option<f64>,
}

/// INI data types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum IniDataType {
    U08,  // 8-bit unsigned
    S08,  // 8-bit signed
    U16,  // 16-bit unsigned
    S16,  // 16-bit signed
    U32,  // 32-bit unsigned
    S32,  // 32-bit signed
    F32,  // 32-bit float
    ASCII, // ASCII string
}

/// INI field types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum IniFieldType {
    Spinner,
    ComboBox,
    CheckBox,
    TextEdit,
    Button,
    Label,
}

/// TunerStudio INI parser
pub struct TunerStudioParser;

impl TunerStudioParser {
    /// Parse a TunerStudio INI file
    pub fn parse<P: AsRef<Path>>(path: P) -> Result<TunerStudioIni> {
        let content = std::fs::read_to_string(path)?;
        Self::parse_content(&content)
    }

    /// Parse INI content from string
    pub fn parse_content(content: &str) -> Result<TunerStudioIni> {
        let mut ini = TunerStudioIni {
            metadata: IniMetadata {
                signature: String::new(),
                version: String::new(),
                query_command: String::new(),
                ecu_name: String::new(),
                firmware_version: String::new(),
            },
            constants: HashMap::new(),
            tables: HashMap::new(),
            dialogs: HashMap::new(),
            menus: HashMap::new(),
            curves: HashMap::new(),
            gauges: HashMap::new(),
        };

        let mut current_section = String::new();
        let mut in_section = false;

        for line in content.lines() {
            let line = line.trim();
            
            // Skip comments and empty lines
            if line.is_empty() || line.starts_with(';') || line.starts_with('#') {
                continue;
            }

            // Check for section headers
            if line.starts_with('[') && line.ends_with(']') {
                current_section = line[1..line.len()-1].to_string();
                in_section = true;
                continue;
            }

            if !in_section {
                continue;
            }

            // Parse based on current section
            match current_section.as_str() {
                "MegaTune" => {
                    Self::parse_metadata_line(line, &mut ini.metadata)?;
                }
                "Constants" => {
                    if let Some(constant) = Self::parse_constant_line(line)? {
                        ini.constants.insert(constant.name.clone(), constant);
                    }
                }
                "Tables" => {
                    if let Some(table) = Self::parse_table_line(line)? {
                        ini.tables.insert(table.name.clone(), table);
                    }
                }
                "Gauges" => {
                    if let Some(gauge) = Self::parse_gauge_line(line)? {
                        ini.gauges.insert(gauge.name.clone(), gauge);
                    }
                }
                _ => {
                    // Handle other sections like dialogs, menus, etc.
                    if current_section.starts_with("Dialog") {
                        // Parse dialog definitions
                    } else if current_section.starts_with("Menu") {
                        // Parse menu definitions
                    }
                }
            }
        }

        Ok(ini)
    }

    /// Parse metadata line
    fn parse_metadata_line(line: &str, metadata: &mut IniMetadata) -> Result<()> {
        if let Some((key, value)) = Self::parse_key_value(line) {
            match key.as_str() {
                "signature" => metadata.signature = value,
                "version" => metadata.version = value,
                "queryCommand" => metadata.query_command = value,
                "ecuName" => metadata.ecu_name = value,
                "firmwareVersion" => metadata.firmware_version = value,
                _ => {}
            }
        }
        Ok(())
    }

    /// Parse constant definition line
    fn parse_constant_line(line: &str) -> Result<Option<IniConstant>> {
        // Example: rpm = scalar, U16, 0, "RPM", 1, 0, 0, 8000, 0
        let parts: Vec<&str> = line.split('=').collect();
        if parts.len() != 2 {
            return Ok(None);
        }

        let name = parts[0].trim().to_string();
        let definition = parts[1].trim();

        if !definition.starts_with("scalar") {
            return Ok(None);
        }

        let params: Vec<&str> = definition.split(',').map(|s| s.trim()).collect();
        if params.len() < 9 {
            return Ok(None);
        }

        let data_type = Self::parse_data_type(params[1])?;
        let offset = params[2].parse::<u32>()?;
        let units = params[3].trim_matches('"').to_string();
        let scale = params[4].parse::<f64>()?;
        let translate = params[5].parse::<f64>()?;
        let min_value = if params[6] != "0" { Some(params[6].parse::<f64>()?) } else { None };
        let max_value = if params[7] != "0" { Some(params[7].parse::<f64>()?) } else { None };
        let digits = params[8].parse::<u8>()?;

        Ok(Some(IniConstant {
            name,
            data_type,
            offset,
            units,
            scale,
            translate,
            min_value,
            max_value,
            digits,
        }))
    }

    /// Parse table definition line
    fn parse_table_line(line: &str) -> Result<Option<IniTable>> {
        // Example: fuelTable = array, U08, 0, [16x16], "ms", 0.1, 0, 0, 25.5, 1
        let parts: Vec<&str> = line.split('=').collect();
        if parts.len() != 2 {
            return Ok(None);
        }

        let name = parts[0].trim().to_string();
        let definition = parts[1].trim();

        if !definition.starts_with("array") {
            return Ok(None);
        }

        // Parse array definition - simplified for now
        Ok(Some(IniTable {
            name,
            x_axis: IniAxis {
                name: "X".to_string(),
                units: "".to_string(),
                data_type: IniDataType::U08,
                scale: 1.0,
                translate: 0.0,
            },
            y_axis: IniAxis {
                name: "Y".to_string(),
                units: "".to_string(),
                data_type: IniDataType::U08,
                scale: 1.0,
                translate: 0.0,
            },
            z_axis: IniAxis {
                name: "Z".to_string(),
                units: "".to_string(),
                data_type: IniDataType::U08,
                scale: 1.0,
                translate: 0.0,
            },
            x_size: 16,
            y_size: 16,
            x_bins: vec![],
            y_bins: vec![],
        }))
    }

    /// Parse gauge definition line
    fn parse_gauge_line(line: &str) -> Result<Option<IniGauge>> {
        // Simplified gauge parsing
        if let Some((name, _definition)) = Self::parse_key_value(line) {
            Ok(Some(IniGauge {
                name,
                title: "Gauge".to_string(),
                variable: "rpm".to_string(),
                units: "RPM".to_string(),
                min_value: 0.0,
                max_value: 8000.0,
                warning_low: None,
                warning_high: None,
                danger_low: None,
                danger_high: None,
            }))
        } else {
            Ok(None)
        }
    }

    /// Parse key-value pair
    fn parse_key_value(line: &str) -> Option<(String, String)> {
        let parts: Vec<&str> = line.splitn(2, '=').collect();
        if parts.len() == 2 {
            Some((parts[0].trim().to_string(), parts[1].trim().to_string()))
        } else {
            None
        }
    }

    /// Parse data type string
    fn parse_data_type(type_str: &str) -> Result<IniDataType> {
        match type_str.trim() {
            "U08" => Ok(IniDataType::U08),
            "S08" => Ok(IniDataType::S08),
            "U16" => Ok(IniDataType::U16),
            "S16" => Ok(IniDataType::S16),
            "U32" => Ok(IniDataType::U32),
            "S32" => Ok(IniDataType::S32),
            "F32" => Ok(IniDataType::F32),
            "ASCII" => Ok(IniDataType::ASCII),
            _ => Err(anyhow!("Unknown data type: {}", type_str)),
        }
    }

    /// Convert TunerStudio INI to universal TuningData
    pub fn to_tuning_data(ini: &TunerStudioIni) -> Result<TuningData> {
        let mut data = TuningData::new();
        
        // Set metadata
        data.metadata.name = ini.metadata.ecu_name.clone();
        data.metadata.source_format = "tunerstudio_ini".to_string();
        data.metadata.version = ini.metadata.version.parse().unwrap_or(1);

        // Convert constants to scalar values
        for (name, constant) in &ini.constants {
            data.scalar_values.insert(name.clone(), ScalarValue {
                name: constant.name.clone(),
                value: 0.0, // Default value, would be loaded from actual tune
                units: constant.units.clone(),
                min_value: constant.min_value,
                max_value: constant.max_value,
                description: format!("TunerStudio constant: {}", name),
            });
        }

        // Convert tables
        for (name, table) in &ini.tables {
            let table_2d = Table2D::new(&table.name, table.x_size, table.y_size);
            
            if name.contains("fuel") {
                data.fuel_tables.insert(name.clone(), table_2d);
            } else if name.contains("ignition") || name.contains("timing") {
                data.ignition_tables.insert(name.clone(), table_2d);
            } else if name.contains("boost") {
                data.boost_tables.insert(name.clone(), table_2d);
            }
        }

        Ok(data)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parse_constant_line() {
        let line = r#"rpm = scalar, U16, 0, "RPM", 1, 0, 0, 8000, 0"#;
        let constant = TunerStudioParser::parse_constant_line(line).unwrap().unwrap();
        
        assert_eq!(constant.name, "rpm");
        assert_eq!(constant.units, "RPM");
        assert_eq!(constant.scale, 1.0);
        assert_eq!(constant.max_value, Some(8000.0));
    }

    #[test]
    fn test_parse_key_value() {
        let line = "signature = \"MS2Extra 3.4.0\"";
        let (key, value) = TunerStudioParser::parse_key_value(line).unwrap();
        
        assert_eq!(key, "signature");
        assert_eq!(value, "\"MS2Extra 3.4.0\"");
    }

    #[test]
    fn test_data_type_parsing() {
        assert!(matches!(TunerStudioParser::parse_data_type("U16").unwrap(), IniDataType::U16));
        assert!(matches!(TunerStudioParser::parse_data_type("F32").unwrap(), IniDataType::F32));
    }
}
