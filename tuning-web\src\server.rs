//! Web server implementation

use crate::{api, websocket, AppState, WebConfig};
use axum::{
    routing::{get, post},
    Router,
};
use std::sync::Arc;
use tower::ServiceBuilder;
use tower_http::{cors::Cors<PERSON>ayer, services::ServeDir};

/// Start the web server
pub async fn start_server(
    config: WebConfig,
    app_state: Arc<AppState>,
) -> anyhow::Result<()> {
    let app = create_app(config.clone(), app_state).await?;

    let addr = format!("{}:{}", config.host, config.port);
    let listener = tokio::net::TcpListener::bind(&addr).await?;
    
    tracing::info!("Web server starting on {}", addr);
    tracing::info!("API endpoint: {}", config.api_path);
    tracing::info!("WebSocket endpoint: {}", config.websocket_path);
    
    axum::serve(listener, app).await?;
    
    Ok(())
}

/// Create the Axum application
async fn create_app(
    config: WebConfig,
    app_state: Arc<AppState>,
) -> anyhow::Result<Router> {
    let mut app = Router::new()
        // API routes
        .route(&config.api_path, post(api::handle_api_request))
        // WebSocket route
        .route(&config.websocket_path, get(websocket::websocket_handler))
        // Health check
        .route("/health", get(health_check))
        // State
        .with_state(app_state);

    // Add CORS if enabled
    if config.enable_cors {
        app = app.layer(
            ServiceBuilder::new()
                .layer(CorsLayer::permissive())
        );
    }

    // Serve static files if path exists
    if std::path::Path::new(&config.static_files_path).exists() {
        app = app.nest_service("/", ServeDir::new(&config.static_files_path));
    }

    Ok(app)
}

/// Health check endpoint
async fn health_check() -> &'static str {
    "OK"
}
