[package]
name = "tuning-formats"
version = "0.1.0"
edition = "2021"
description = "Universal tuning file format conversion (.msq ↔ .yaml ↔ .json)"
license = "MIT OR Apache-2.0"

[dependencies]
# Workspace dependencies
serde = { workspace = true }
serde_json = { workspace = true }
serde_yaml = { workspace = true }
anyhow = { workspace = true }
thiserror = { workspace = true }

# Format-specific dependencies
quick-xml = { version = "0.31", features = ["serialize"] }
csv = "1.3"
toml = "0.8"
bincode = "1.3"

# Utilities
regex = "1.10"
chrono = { workspace = true }
uuid = { workspace = true }
