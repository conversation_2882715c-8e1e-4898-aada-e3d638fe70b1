//! Device management and abstraction

use crate::{DeviceInfo, DeviceCapabilities};

/// Generic device implementation
#[derive(Debug)]
pub struct GenericDevice {
    info: DeviceInfo,
    capabilities: DeviceCapabilities,
    connected: bool,
}

impl GenericDevice {
    pub fn new(info: DeviceInfo, capabilities: DeviceCapabilities) -> Self {
        Self {
            info,
            capabilities,
            connected: false,
        }
    }
}
