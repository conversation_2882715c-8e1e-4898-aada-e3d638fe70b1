[package]
name = "tuning-core"
version = "0.1.0"
edition = "2021"
description = "Core tuning engine with torque-centric architecture"
license = "MIT OR Apache-2.0"

[dependencies]
# Workspace dependencies
tokio = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
serde_yaml = { workspace = true }
anyhow = { workspace = true }
thiserror = { workspace = true }
tracing = { workspace = true }
uuid = { workspace = true }
chrono = { workspace = true }

# Local dependencies
tuning-hal = { path = "../tuning-hal" }
tuning-formats = { path = "../tuning-formats" }

# Core-specific dependencies
async-trait = "0.1"
futures = "0.3"
parking_lot = "0.12"

[features]
default = ["std"]
std = []
wasm = ["wasm-bindgen", "js-sys", "web-sys"]

[dependencies.wasm-bindgen]
version = "0.2"
optional = true

[dependencies.js-sys]
version = "0.3"
optional = true

[dependencies.web-sys]
version = "0.3"
optional = true
features = [
  "console",
  "WebSocket",
  "MessageEvent",
  "ErrorEvent",
  "CloseEvent",
]
