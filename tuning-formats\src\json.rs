//! JSON format support

use crate::TuningData;
use anyhow::Result;
use std::path::Path;

/// JSON format handler
pub struct JsonFormat;

impl JsonFormat {
    /// Load JSON file
    pub fn load<P: AsRef<Path>>(path: P) -> Result<TuningData> {
        let content = std::fs::read_to_string(path)?;
        let mut data: TuningData = serde_json::from_str(&content)?;
        data.metadata.source_format = "json".to_string();
        Ok(data)
    }

    /// Save to JSON file
    pub fn save<P: AsRef<Path>>(data: &TuningData, path: P) -> Result<()> {
        let content = serde_json::to_string_pretty(data)?;
        std::fs::write(path, content)?;
        Ok(())
    }
}
