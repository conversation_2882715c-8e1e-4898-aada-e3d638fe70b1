@echo off
echo Universal Tuning Software - Build Script
echo ========================================

echo.
echo Checking Rust installation...
rustc --version
if %errorlevel% neq 0 (
    echo ERROR: Rust is not installed or not in PATH
    echo Please install Rust from https://rustup.rs/
    pause
    exit /b 1
)

cargo --version
if %errorlevel% neq 0 (
    echo ERROR: Cargo is not available
    pause
    exit /b 1
)

echo.
echo Rust installation OK!
echo.

echo Checking project structure...
cargo check
if %errorlevel% neq 0 (
    echo ERROR: Project check failed
    pause
    exit /b 1
)

echo.
echo Project check passed!
echo.

echo Building project...
cargo build
if %errorlevel% neq 0 (
    echo ERROR: Build failed
    pause
    exit /b 1
)

echo.
echo Build successful!
echo.

echo Starting application...
echo Web interface will be available at: http://localhost:8080
echo Press Ctrl+C to stop the application
echo.

cargo run

pause
